<template>
    <div class="box">
        <span v-for="item in imgList" class="imgList"
            :style="{ 'width': props.width + 'px', 'height': props.height + 'px' }" v-if="imgList.length != 0">
            <span>
                <div class="close">
                    <span class="text"><el-icon size="16px" @click="viewPic(item)">
                            <Search />
                        </el-icon>
                        <el-icon size="16px" @click="delPic(item)">
                            <Delete />
                        </el-icon></span>
                </div>
                <el-image :src="returnUrl(item)" style="width: 100%;height: 100%;" ref="previewImg" v-if="type === 'pic'"
                    :preview-src-list="[returnUrl(item)]" :preview-teleported="true">
                    <template #error>
                        <div class="image-slot">
                            <el-icon>
                                <Picture />
                            </el-icon>
                        </div>
                    </template></el-image>
                <video :src="returnUrl(item)" style="width: 100%;height: 100%;" v-else></video>

            </span>
        </span>
        <div class="upLoadPicBox" @click="handUploadPic" v-show="imgList.length < props.size">
            <div class="upLoad"><el-icon size="26px">
                    <Camera />
                </el-icon>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { getPicUrl } from '@/utils/index'
import { getCurrentInstance, ref, watch, watchEffect} from "vue"
const props = defineProps({
    size: {
        type: Number,
        default: 1
    },
    modelValue: {
        type: String,
        default: ''
    },
    width: {
        type: Number,
        default: 48
    },
    height: {
        type: Number,
        default: 48
    },
})/* 判断是否有http */
const returnUrl = (url: string) => {
    const newUrl = url.indexOf("http");
    if (newUrl == 0) {
        // "字符串是以http开头的！"
        return url
    }
    if (newUrl == -1) {
        //   "字符串不是以http开头的！"
        return getPicUrl() + url
    }
}
const emit = defineEmits(['update:modelValue'])
const imgList = ref<string[]>([])
const type = ref('pic')
watchEffect(() => {
    if (props.modelValue) imgList.value = props.modelValue.split(',');
    if (imgList.value.find(item => item.indexOf('.mp4') != -1)) {
        type.value = 'video'
    }
    })

const { proxy }: any = getCurrentInstance()
const handUploadPic = () => {
    proxy.$subDialog({
        size: props.size - imgList.value.length,
        saveClick: async (val: any) => {
            let arr = val.map((item: any) => {
                return item.sattDir
            })
            imgList.value.push(...arr)
            emit('update:modelValue', imgList.value.toString())
        }
    });
}
const delPic = (url: string) => {
    let index = imgList.value.findIndex((value: string) => value === url)
    imgList.value.splice(index, 1)
    emit('update:modelValue', imgList.value.toString())
}
const previewImg = ref()
const viewPic = (item: string) => {
    if (type.value === 'pic') {
        previewImg.value[0].showViewer = true
    } else {
        window.open(returnUrl(item), "_blank")

    }
}
</script>

<style lang="scss" scoped>
.box {
    display: flex;
    align-items: center;
    justify-content: center;

    .upLoadPicBox {
        display: inline-block;
        cursor: pointer;

        .upLoad {
            width: 48px;
            height: 48px;
            line-height: 48px;
            border: 1px dotted rgba(0, 0, 0, .1);
            border-radius: 4px;
            background: rgba(0, 0, 0, .02);
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: center;
            -ms-flex-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;

            .cameraIconfont {
                color: #898989;
            }
        }
    }

    .imgList {
        margin-right: 5px;
        position: relative;

        .close {
            position: absolute;
            width: 100%;
            height: 100%;
            z-index: 2000;
            display: flex;
            align-items: center;
            justify-content: center;

            .text {
                z-index: 2000;
                display: none;
                justify-content: space-around;
                align-items: center;
                background-color: rgba(0, 0, 0, 0.2);
                width: 100%;
                height: 100%;
                font-size: 12px;
                color: white;
                cursor: pointer;
            }

            &:hover {
                .text {
                    display: flex
                }
            }
        }
    }
}

:deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .02);
    color: var(--el-text-color-secondary);
    font-size: 30px;
    border: 1px dotted rgba(0, 0, 0, .1);
    border-radius: 4px;
}
</style>
