{"compilerOptions": {"suppressImplicitAnyIndexErrors": true, "target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "types": ["vite/client", "node", "element-plus/global"], "typeRoots": ["../node_modules/@types"], "baseUrl": "./", "paths": {"@/*": ["src/*"], "types": ["vite,client"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "src/api/role.js", "src/components/tinymce/zh_CN.JS", "src/components/tinymce/zh-Hans.js"]}