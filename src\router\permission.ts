/*
 * @Date: 2022-07-24 21:43:47
 * @Description:
 */
/** 引入类型 */
import type { Route } from './index.type'
/** 引入路由相关的资源 */
import router, { modules } from './index'
/** 引入vuex实例 */
import store from '@/store'
import { getMenus } from '@/api/user'
/** 动态路由实现基础组件 */
/** 引入全局Layout组件 */
import Layout from '@/layout/index.vue'
/** 引入多级菜单控制器组件 */
import MenuBox from '@/components/menu/index.vue'
import routesList from './routesList'

/* 寻找对象深层属性 */
/* getPath = mb(list)
getPath(routesList)
*/
let mb = (p: any[]) => (o: any) =>
  p.map((c: string | number) => (o = (o || {})[c])) && o
const createComponents = (path: string) => {
  let list = path.split('/')
  list.shift()
  let getPath = mb(list)
  const component = getPath(routesList)
  if (component) return component
  return false
  // return createNameComponent(() => import('@/views/main/dashboard/index.vue'))
}

// 过滤函数，为了过滤menuType为A的每一项
function filterMenu(menuList: any) {
  return menuList
    .filter((item: any) => item.menuType !== 'A') // 过滤掉menuType为A的项
    .map((item: any) => {
      if (item.childList && item.childList.length > 0) {
        // 如果有子菜单，递归过滤子菜单
        return {
          ...item,
          childList: filterMenu(item.childList),
        }
      }
      return item
    })
}
const getComponents = (data: any, level = 1) => {
  // 进行过滤
  const filteredMenu = filterMenu(data)
  const processedRoutes = filteredMenu.map((item: any) => {
    item.meta = { title: item.name }
    item.path = item.component
    // item.redirect = item.childList[0].component
    if (item.menuType === 'M' || item.childList.length != 0) {
      if (level === 1) {
        item.component = Layout
      } else {
        item.component = MenuBox
      }
      item.meta.icon = `iconfont ${item.icon}`
      if (item.path != '/') item.alwayShow = true
      item.children = getComponents(item.childList, level + 1)
      if (item.children.length > 0) {
        item.redirect = item.children[0].path
      }
      return item
    } else {
      item.component = createComponents(item.component)
      if (item.name == '首页' && item.menuType === 'C') {
        item.meta.hideClose = true
      }
      // /* 不展示在左侧菜单栏的页面 */
      // if (item.path === '/product/createproduct') {
      //   item.hideMenu = true
      //   item.meta.activeMenu = '/product/manage'
      // }
      if (!item.isShow) {
        item.hideMenu = true
      }
      return item
    }
  })
  return processedRoutes
}
/**
 * @name 动态路由的权限新增，供登录后调用
 * @other 如果需要进行后端接口控制菜单的话，请在此拿到后端的菜单树与asyncRoutes对比，生成一个新的值
 */
async function addRoutes() {
  let data = null
  await getMenus().then(res => {
    data = res
  })
  const list = getComponents(data)
  return new Promise<void>(resolve => {
    list.forEach((item: Route) => {
      modules.push(item)
      router.addRoute(item)
    })
    resolve()
  })
  // 已验证完成，下面代码添加的可以实时同步至菜单中去，可以添加setTimeout(() => {}) 模拟异步代码的操作
  // 利用前端路由表模拟后端数据问题
}

/**
 * @des 登录了之后会执行这个方法，实现动态路由的功能
 */
export async function getAuthRoutes() {
  // 判断token是否存在，存在则调用添加路由的方法
  if (store.state.user.token) {
    await addRoutes()
  }
}
