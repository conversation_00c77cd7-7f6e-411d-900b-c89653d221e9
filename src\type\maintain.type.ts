
/**
 * 组合数据列表
 */
export interface GroupListParams {
    keywords?: string;
    limit?: number;
    page?: number;
}

/**
 * 组合数据列表
 */
export interface GroupListRes {
    /**
     * @name 表单id
     */
    formId?: number;
    /**
     * @name 创建时间
     */
    createTime?: Date;
    /**
     * @name 数据组名称
     */
    name?: string;
    /**
     * @name 更新时间
     */
    updateTime?: Date;
    /**
     * @name 简介
     */
    info?: string;
    /**
     * @name 组合数据ID
     */
    id?: number;
    loading?:boolean;
}

/**
 * 新增、编辑、删除组合数据
 */
export interface GroupParams {
    id:number;
    /**
     * @name 表单id
     */
    formId?: number;
    /**
     * @name 数据组名称
     */
    name?: string;
    /**
     * @name 简介
     */
    info?: string;
}






/**
 * 组合数据 数据列表  表格数据类型
 */
export interface GroupDataListParams {
    /**
     * @name 分组id
     */
    gid?: number;
    /**
     * @name 关键字
     */
    keywords?: string;
    limit?: number;
    page?: number;
    /**
     * @name 状态（1：开启；2：关闭；）
     */
    status?: string;
}


/**
 * 组合数据 数据列表  表格数据类型
 */
export interface GroupDataListRes {
    /**
     * @name 创建时间
     */
    createTime?: Date;
    /**
     * @name 对应的数据组id
     */
    gid?: number;
    /**
     * @name 组合数据详情ID
     */
    id?: number;
    /**
     * @name 数据排序
     */
    sort?: number;
    /**
     * @name 状态（1：开启；0：关闭；）
     */
    status?: boolean;
    /**
     * @name 更新时间
     */
    updateTime?: Date;
    /**
     * @name 数据组对应的数据值（json数据）
     */
    value?: string;
}



/**
 * 组合数据 新增
 */
export interface GroupDataParams {
    form?: SystemFormCheckRequest;
    /**
     * @name 对应的数据组id
     */
    gid?: number;
    listDataId?:number;
}

/**
 * SystemFormCheckRequest，表单数据
 */
export interface SystemFormCheckRequest {
    /**
     * @name 字段值列表
     */
    fields: SystemFormItemCheckRequest[];
    /**
     * @name 表单名称
     */
    id: number;
    /**
     * @name 排序
     */
    sort: number;
    /**
     * @name 状态（1：开启；0：关闭；）
     */
    status?: boolean;
}

/**
 * 字段值列表
 */
export interface SystemFormItemCheckRequest {
    /**
     * @name 字段名称
     */
    name: string;
    /**
     * @name 字段显示文字
     */
    title: string;
    /**
     * @name 字段值
     */
    value: string;
}
