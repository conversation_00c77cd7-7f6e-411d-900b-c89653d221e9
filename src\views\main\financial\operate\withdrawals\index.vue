<template>
  <div class="layout-container">
    <div style="padding: 15px">
      <el-form label-position="right" label-width="100px">
        <el-form-item label="时间选择">
          <timepicker v-model="query.dateLimit" @change="getTableData(true)" />
        </el-form-item>

        <Unfold>
          <el-row>
            <el-form-item label="状态">
              <el-radio-group
                v-model="query.status"
                @change="getTableData(true)"
              >
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="-1">未通过</el-radio-button>
                <el-radio-button label="0">审核中</el-radio-button>
                <el-radio-button label="1">已通过</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="提现目标">
              <el-radio-group
                v-model="query.extractTarget"
                @change="getTableData(true)"
              >
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button
                  v-for="(val, key) in rechargeTargetList"
                  :index="key"
                  :label="key"
                >
                  {{ val }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="提现方式">
              <el-radio-group
                v-model="query.extractType"
                @change="getTableData(true)"
              >
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="weixin">微信</el-radio-button>
                <el-radio-button label="alipay">支付宝</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-row>
          <el-row>
            <el-form-item label="账号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.account"
                placeholder="请输入账号"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.phone"
                placeholder="请输入手机号"
              ></el-input>
            </el-form-item>
            <el-form-item label="昵称">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.nickname"
                placeholder="请输入昵称"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-button
              type="primary"
              :icon="Search"
              class="search-btn"
              @click="getTableData(true)"
            >
              {{ $t('message.common.search') }}
            </el-button>
          </el-row>
        </Unfold>
      </el-form>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column prop="id" label="ID" align="center" width="80" />
        <el-table-column prop="uid" label="用户id" align="center">
          <template #default="scope">
            <div>
              <RightMenu :uid="scope.row.uid"></RightMenu>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="account" label="账号" align="center" />
        <el-table-column prop="phone" label="手机号" align="center" />
        <el-table-column prop="nickName" label="昵称" align="center" />
        <el-table-column prop="extractTarget" label="提现方式" align="center">
          <template #default="scope">
            <span v-if="scope.row.extractType == 'weixin'">微信</span>
            <span v-else-if="scope.row.extractType == 'alipay'">支付宝</span>
          </template>
        </el-table-column>
        <el-table-column label="提现目标" align="center">
          <template #default="scope">
            {{ rechargeTargetList[scope.row.extractTarget] }}
          </template>
        </el-table-column>
        <el-table-column prop="" label="提现明细" align="center">
          <template #default="scope">
            <div>提现金额:{{ scope.row.extractPrice }}</div>
            <div>实到金额:{{ scope.row.actualPrice }}</div>
            <div>手续费:{{ scope.row.commission }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="extractType" label="状态" align="center">
          <template #default="scope">
            <div>
              <el-tag type="danger" v-if="scope.row.status == -1">
                未通过
              </el-tag>
              <el-tag type="info" v-else-if="scope.row.status == 0">
                审核中
              </el-tag>
              <el-tag type="success" v-if="scope.row.status == 1">通过</el-tag>
            </div>
            <div v-if="scope.row.status == -1" style="color: #f56c6c">
              失败原因:{{ scope.row.failMsg }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="mark" label="备注" align="center" />
        <el-table-column prop="createTime" label="发起时间" align="center" />
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <el-button
              @click="handleEdit(scope.row)"
              :type="scope.row.status == 0 ? 'primary' : ''"
            >
              {{ scope.row.status == 0 ? '审核' : '查看' }}
            </el-button>
          </template>
        </el-table-column>
      </Table>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { rechargeTargetList } from '@/utils/contants'
  import { ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import { getExtractList } from '@/api/financial'
  import { LayerInterface } from '@/components/layer/index.vue'
  import Table from '@/components/table/index.vue'
  import Layer from './layer.vue'
  import Unfold from '@/components/unfold/index.vue'
  import { Search } from '@element-plus/icons-vue'
  import RightMenu from '@/components/rightMenu/index.vue'
  import timepicker from '@/components/timePicker/index.vue'
  import type { ExtractListRes } from '@/type/financial.type'

  // 存储搜索用的数据
  const query = reactive({
    account: '',
    phone: '',
    nickname: '',
    status: 0,
    extractType: '',
    extractTarget: 0,
    dateLimit: '',
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<ExtractListRes[]>([])

  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getExtractList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 编辑弹窗功能
  const handleEdit = (row: any) => {
    layer.title = '查看详情'
    layer.row = row
    layer.show = true
  }
  getTableData(true)
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }
</style>
