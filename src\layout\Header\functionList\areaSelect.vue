<template>
  <div class="flex-center">当前管理区域：
    <el-select v-model="areaId" class="select_style" placeholder="选择管理区域" @change="changeArea">
      <el-option v-for="item in areaOptions" :key="item.id" :label="item.areaName" :value="item.id" />
    </el-select>
  </div>
</template>

<script setup lang="ts">
import { useStore } from 'vuex'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import {changeCurrentArea, selfAreaList} from "@/api/units";
import { useRouter } from 'vue-router'
const store = useStore()
const router = useRouter()
const areaOptions = ref()
const areaId = ref(store.state.user.info.currentArea || null)
selfAreaList().then(res => {
  areaOptions.value = res
})
const changeArea = async (e: number) => {
  try {
    await changeCurrentArea(e)
    store.dispatch('user/getInfo')
    router.push({ path: '/' })
  } catch (e: any) {
    ElMessage.error(e.message)
  }
}
</script>

<style scoped lang="scss">
.flex-center {
  display: flex;
  align-items: center;
}
.select_style {
  width: 120px;
  :deep(.el-input__inner) {
    height: 30px;
  }
}
</style>