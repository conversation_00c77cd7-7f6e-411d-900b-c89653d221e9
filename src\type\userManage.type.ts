/***@name  用户列表请求体 */
export interface UserListParams {
  /*** @name 手机号*/
  phone?: string
  /*** @name 用户昵称*/
  nickName?: string
  /*** @name 日期*/
  dateLimit?: string
  /*** @name 页数*/
  page?: number
  /*** @name 每页条数*/
  limit?: number
}
/***@name  用户信息响应体 */
export interface UserListRes {
  /*** @name 用户账号*/
  account?: string
  /** * @name 添加ip*/
  addIp?: string
  /** * @name 用户头像*/
  avatar?: string
  /** * @name 生日*/
  birthday?: string
  /** * @name 身份证号码*/
  cardId?: string
  /** * @name 城市*/
  city?: string
  /** * @name 清除时间*/
  cleanTime?: string
  /** * @name 创建时间*/
  createTime?: string
  /** * @name 剩余积分*/
  integral?: number
  /** * @name 邀请码*/
  inviteCode?: string
  /** * @name 最后一次登录ip*/
  lastIp?: string
  /***  @name 最后一次登录时间*/
  lastLoginTime?: string
  /**
   *  @name 用户登陆类型，h5,wechat,routine
   */
  loginType?: string
  /**
   *  @name 用户备注
   */
  mark?: string
  /**
   * @name 用户余额
   */
  money?: number
  /**
   * @name 用户昵称
   */
  nickName?: string
  /**
   * @name 推广等级记录
   */
  path?: string
  /**
   * @name 手机号码
   */
  phone?: string
  /**
   * @name 支付密码
   */
  payPassword?: string
  /**
   * @name 用户密码
   */
  pwd?: string
  /**
   *@name 真实姓名
   */
  realName?: string
  /**
   *@name 性别，0未知，1男，2女，3保密
   */
  sex?: number
  /**
   *@name 推广员id
   */
  spreadUid?: number
  /**
   *@name 1为正常，0为禁止
   */
  status?: boolean
  /**
   *@name 微信openid
   */
  wxOpenId?: string
  /**
   *@name 支付宝账号
   */
  zhifubaoAccount?: string
  /**
   *@name 支付宝名称
   */
  zhifubaoName?: string
  /**
   * @name 用户id
   */
  uid?: number
  /**
   *@name 更新时间
   */
  updateTime?: string
  /**
   *@name 是否关注公众号0未关注，1已关注
   */
  subscribe?: string
  /**
   *@name 关注公众号时间
   */
  subscribeTime?: string
}

/***@name 后台修改用户货币请求体*/
export interface UpdateFinanceParams {
  /**
   *@name 数额
   */
  amount: number
  /**
   *@name 货币类型 1 余额 2 积分 3 通证 4 通宝 5 奖金
   */
  targetType: number
  /**
   *@name 加减类型   1增加 2减少
   */
  type: number
  /**
   *@name 用户id
   */
  uid: number
}
/***@name  用户详情请求体 */
export interface UserInfoParams{
    /***@name  用户id */
    id:number
}