<template>
  <div v-if="shouldShowCategory">
    <div
      class="category-item"
      :style="{ paddingLeft: `${level * 16 + 8}px` }"
      @click="handleSelect"
    >
      <div class="category-content">
        <el-button
          v-if="hasChildren"
          text
          size="small"
          class="expand-button"
          @click.stop="toggleExpanded"
        >
          <el-icon>
            <ArrowDown v-if="isExpanded" />
            <ArrowRight v-else />
          </el-icon>
        </el-button>
        <div v-else class="expand-placeholder"></div>

        <span
          class="category-name"
          :class="{ selected: selectedId === category.id }"
        >
          {{ category.name }}
        </span>
      </div>
    </div>

    <template v-if="isExpanded && hasChildren">
      <CategoryTreeNode
        v-for="child in category.child"
        :key="child.id"
        :category="child"
        :level="level + 1"
        :selected-id="selectedId"
        :search-text="searchText"
        @select="$emit('select', $event)"
      />
    </template>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed } from 'vue'
  import { ArrowDown, ArrowRight } from '@element-plus/icons-vue'
  import type { TreeCategroyRes } from '@/type/category.type'

  interface Props {
    category: TreeCategroyRes
    level: number
    selectedId: number
    searchText: string
  }

  const props = defineProps<Props>()

  const emit = defineEmits<{
    select: [{ id: number; name: string }]
  }>()

  const isExpanded = ref(true) // 默认展开

  const hasChildren = computed(() => {
    return props.category.child && props.category.child.length > 0
  })

  const shouldShowCategory = computed(() => {
    if (!props.searchText) return true

    // 如果当前分类匹配搜索条件
    if (
      props.category.name
        ?.toLowerCase()
        .includes(props.searchText.toLowerCase())
    ) {
      return true
    }

    // 如果有子分类匹配搜索条件
    if (hasChildren.value) {
      return hasChildrenMatching(props.category.child!, props.searchText)
    }

    return false
  })

  const hasChildrenMatching = (
    children: TreeCategroyRes[],
    searchTerm: string
  ): boolean => {
    return children.some(child => {
      if (child.name?.toLowerCase().includes(searchTerm.toLowerCase())) {
        return true
      }
      if (child.child && child.child.length > 0) {
        return hasChildrenMatching(child.child, searchTerm)
      }
      return false
    })
  }

  const toggleExpanded = () => {
    isExpanded.value = !isExpanded.value
  }

  const handleSelect = () => {
    if (props.category.id && props.category.name) {
      emit('select', { id: props.category.id, name: props.category.name })
    }
  }
</script>

<style lang="scss" scoped>
  .category-item {
    cursor: pointer;
    padding: 2px 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
    margin: 1px 0;

    &:hover {
      background-color: #f5f7fa;
    }
  }

  .category-content {
    display: flex;
    align-items: center;
    gap: 2px;
    width: 100%;
    min-height: 24px;
  }

  .expand-button {
    width: 16px;
    height: 16px;
    padding: 0;
    min-height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: transparent;
    flex-shrink: 0;

    &:hover {
      background-color: #e6f7ff;
      border-radius: 2px;
    }

    .el-icon {
      font-size: 12px;
      color: #666;
    }
  }

  .expand-placeholder {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }

  .category-name {
    flex: 1;
    font-size: 14px;
    color: #606266;

    &.selected {
      color: #409eff;
      font-weight: 500;
    }
  }
</style>
