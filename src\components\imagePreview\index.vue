<template>
    <div v-if="props.isPic==='true'">    <el-image :style="{ 'width': props.width + 'px', 'height': props.height + 'px' }" :src="returnUrl"
        :preview-src-list="[returnUrl]" :hide-on-click-modal="true" :preview-teleported="true" v-if="props.type == 1">
        <template #error>
            <div class="image-slot">
                <el-icon>
                    <Picture />
                </el-icon>
            </div>
        </template></el-image>
    <el-popover :placement="props.placement" trigger="hover" v-else-if="props.type == 2">
        <template #reference>
            <el-image :src="returnUrl" :style="{ 'width': props.width + 'px', 'height': props.height + 'px' }">
                <template #error>
                    <div class="image-slot">
                        <el-icon>
                            <Picture />
                        </el-icon>
                    </div>
                </template></el-image>

        </template>
        <el-image :src="returnUrl" style="width: 100%;" />
    </el-popover>
    <el-image :style="{ 'width': props.width + 'px', 'height': props.height + 'px' }" :src="imglist[0]" size
        :preview-src-list="imglist" :hide-on-click-modal="true" :preview-teleported="true" v-else-if="props.type == 3">
        <template #error>
            <div class="image-slot">
                <el-icon>
                    <Picture />
                </el-icon>
            </div>
        </template></el-image></div>
<div v-else>
    <video  :src="returnUrl"   :style="{ 'width': props.width + 'px', 'height': props.height + 'px' }" @click="showVideo"></video>
</div>
</template>

<script setup lang="ts">


import { defineProps, ref, computed } from "vue"
import { getPicUrl } from '@/utils/index'
const props = withDefaults(defineProps<{
    type?: number, //1是点击预览  2是滑动预览  3预览多图
    imgurl: string[] | string,
    width?: number,
    height?: number,
    placement?: string,
    isPic?:string /*为false时是视频 */
}>(), {
    //默认值
    type: 1,
    placement: 'top',
    width: 80,
    height: 80,
    isPic:'true'
})
const imglist = ref<any>([])
    if (props.imgurl  && props.type == 3) {
    let list=props.imgurl.split(',')
    imglist.value = list.map((item: string) => {
        return getPicUrl()+item
    });
    
}
/* 判断是否有http */
const returnUrl = computed(() => {
    let newUrl = -1
    if (props.imgurl) {
        newUrl = props.imgurl.indexOf("http")
    }
    if (newUrl == 0) {
        // "字符串是以http开头的！"
        return props.imgurl
    }
    if (newUrl == -1) {
        //   "字符串不是以http开头的！"
        return getPicUrl() + props.imgurl
    }
})
const showVideo=() => {
    window.open(returnUrl.value,'_blank')
}
</script>
<style lang="scss" scoped>
:deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .02);
    color: var(--el-text-color-secondary);
    font-size: 30px;
    border: 1px dotted rgba(0, 0, 0, .1);
    border-radius: 4px;
}

:deep(.el-icon) {
    font-size: 30px;
}
</style>
