<template>
  <div class="layout-container" style="padding: 15px">
    <el-tabs v-model="query.type" class="demo-tabs" @tab-click="clickTab">
      <el-tab-pane
        :label="item.name + `(${item.count})`"
        :name="item.type"
        v-for="item in tabsList"
        :key="item.type"
      ></el-tab-pane>
    </el-tabs>
    <ProductSteps :active-index="activeIndex" :step-title="stepTitle" />
    <ProductSearch
      v-if="activeIndex === 0"
      v-model:keywords="query.keywords"
      v-model:cateId="query.cateId"
      :options="options"
      @search="getTableData(true)"
    />
    <div class="layout-container-table">
      <ProductTable
        ref="productTableRef"
        v-show="activeIndex === 0"
        v-model:page="page"
        :loading="loading"
        :data="tableData"
        @getTableData="getTableData"
        @selection-change="handleSelectionChange"
      />
      <skuInfo
        ref="skuInfoRef"
        v-show="activeIndex === 1"
        :list="handleSkuList"
        :type="route.query.type as string"
        @addActiveIndex="handleSubmitSuccess"
        @removeItem="handleRemoveItem"
      ></skuInfo>
      <!-- 第三页：完成页面 -->
      <div v-show="activeIndex === 2" class="success-page">
        <div class="success-container">
          <div class="success-card">
            <div class="success-icon-wrapper">
              <div class="success-icon-bg">
                <div class="success-icon">
                  <svg viewBox="0 0 1024 1024" width="48" height="48">
                    <path
                      fill="#ffffff"
                      d="M912 190h-69.9c-9.8 0-19.1 4.5-25.1 12.2L404.7 724.5 207 474a32 32 0 0 0-25.1-12.2H112c-6.7 0-10.4 7.7-6.3 12.9l273.9 347c12.8 16.2 37.4 16.2 50.3 0l488.4-618.9c4.1-5.1.4-12.8-6.3-12.8z"
                    />
                  </svg>
                </div>
              </div>
            </div>

            <div class="success-content">
              <h2 class="success-title">入库申请提交成功</h2>
              <p class="success-subtitle">您的入库申请已成功提交</p>
              <div class="success-details">
                <div class="detail-item">
                  <span class="detail-label">申请时间：</span>
                  <span class="detail-value">
                    {{ new Date().toLocaleString() }}
                  </span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">处理状态：</span>
                  <span class="detail-value status-processing">
                    系统处理完成
                  </span>
                </div>
              </div>
            </div>

            <div class="success-actions">
              <el-button
                type="primary"
                size="large"
                class="action-button primary-button"
                @click="startNewProcess"
              >
                <span class="button-icon">+</span>
                开始新的入库流程
              </el-button>
              <el-button
                size="large"
                class="action-button secondary-button"
                @click="cancel"
              >
                <span class="button-icon">←</span>
                返回产品列表
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <StepNavigation
      :active-index="activeIndex"
      :selected-count="allSelectedData.length"
      @next="clickNext"
      @prev="activeIndex--"
      @submit="handleRules"
      @cancel="cancel"
    />

    <!-- 入库确认弹窗 -->
    <InboundConfirmDialog
      v-model:visible="showConfirmDialog"
      :phone-number="confirmDialogData.phoneNumber"
      :remarks="confirmDialogData.remarks"
      :products="confirmDialogData.products"
      @confirm="handleConfirmSubmit"
      @cancel="handleConfirmCancel"
    />
  </div>
</template>
<script lang="ts" setup>
  import { ref, reactive, computed, nextTick } from 'vue'
  import { Page } from '@/components/table/type'
  import skuInfo from './skuInfo.vue'
  import { ElMessage } from 'element-plus'
  import { getAttrValueList } from '@/api/product'
  import { treeCategroy } from '@/api/categoryApi'
  import type { ProductHeaderRes } from '@/type/product.type'
  import { useRouter, useRoute } from 'vue-router'
  import { TreeCategroyRes } from '@/type/category.type'
  import type { ProductListRes } from '@/type/product.type'
  // 导入新组件
  import {
    ProductSteps,
    ProductSearch,
    ProductTable,
    StepNavigation,
    InboundConfirmDialog,
  } from './components'
  const router = useRouter()
  const route = useRoute()

  // 添加表格引用
  const productTableRef = ref()

  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
    cateId: '',
  })
  console.log(route.query.type, 'route')
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<ProductListRes[]>([])
  const activeIndex = ref(0)

  // 获取表格数据
  // params <init> boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init?: boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: any = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getAttrValueList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)

        // 如果有缓存的选中数据，自动选中对应的行
        if (allSelectedData.value.length > 0) {
          nextTick(() => {
            setTimeout(() => {
              autoSelectRowsFromCache()
            }, 100) // 增加延迟确保表格完全渲染
          })
        }
      })
      .catch(() => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 从缓存中自动选中行的函数
  const autoSelectRowsFromCache = () => {
    // 尝试多种方式访问表格实例
    let elTable = null

    if (productTableRef.value?.table?.table) {
      elTable = productTableRef.value.table.table
    } else if (productTableRef.value?.table) {
      elTable = productTableRef.value.table
    } else if (productTableRef.value?.$refs?.table) {
      elTable = productTableRef.value.$refs.table
    }

    if (!elTable) {
      return
    }

    // 根据全局缓存数据选中对应的行
    allSelectedData.value.forEach(cachedItem => {
      const matchedRow = tableData.value.find(
        row =>
          row.id === cachedItem.id ||
          ((row as any).productId === cachedItem.productId &&
            (row as any).suk === cachedItem.suk)
      )

      if (matchedRow && elTable.toggleRowSelection) {
        // 使用匹配的行进行选中操作
        elTable.toggleRowSelection(matchedRow, true)
      }
    })
  }
  const tabsList = ref<ProductHeaderRes[]>([])

  //切换tab
  const clickTab = () => {
    getTableData(true)
  }
  //商品分类选择框
  const options = ref<TreeCategroyRes[]>([])
  treeCategroy({ type: 1, status: -1 }).then(res => [(options.value = res)])

  const handleSkuList = ref<ProductListRes[]>([])

  // 存储所有已选中的数据（跨页面缓存）
  const allSelectedData = ref<ProductListRes[]>([])

  // 去重函数，确保数据唯一性
  const removeDuplicates = (data: ProductListRes[]) => {
    const seen = new Set()
    return data.filter(item => {
      const key = item.id || `${(item as any).productId}-${(item as any).suk}`
      if (seen.has(key)) {
        return false
      }
      seen.add(key)
      return true
    })
  }

  const handleSelectionChange = (val: ProductListRes[]) => {
    // 更新全局缓存，但不要轻易修改 handleSkuList
    // 只更新全局缓存用于表格选中状态的维护
    const currentPageIds = tableData.value.map(item => item.id)

    // 移除当前页面的数据
    allSelectedData.value = allSelectedData.value.filter(
      item => !currentPageIds.includes(item.id)
    )

    // 添加当前页面新选中的数据
    allSelectedData.value.push(...val)

    // 去重
    allSelectedData.value = removeDuplicates(allSelectedData.value)

    // 保持原有的 handleSkuList 更新逻辑
    handleSkuList.value = val
  }

  // 确认弹窗相关状态
  const showConfirmDialog = ref(false)
  const confirmDialogData = ref({
    phoneNumber: '',
    remarks: '',
    products: [],
  })

  const clickNext = () => {
    if (!allSelectedData.value.length && activeIndex.value === 0) {
      return ElMessage.error('请选择至少一个货品！')
    } else {
      // 从第一步进入第二步时，需要同步数据
      if (activeIndex.value === 0) {
        // 使用全局缓存的数据更新 handleSkuList
        handleSkuList.value = [...allSelectedData.value]
      }
      activeIndex.value++
    }
  }
  const cancel = () => {
    router.push('/product/productSkuList')
  }
  const stepTitle = computed(() => {
    return `填写${(route.query.type as string) === '1' ? '入' : '出'}库信息`
  })
  const skuInfoRef = ref()
  const handleRules = async () => {
    try {
      /* route.query.type 1: 入库   2: 出库 */
      if ((route.query.type as string) === '2') {
        // 出库逻辑保持不变
        await skuInfoRef.value.onSubmit()
      } else {
        // 入库逻辑：先验证数据，然后显示确认弹窗
        if (!skuInfoRef.value) return

        // 获取表单数据进行验证
        const formData = skuInfoRef.value.form || {}

        // 验证是否有入库商品
        if (
          !formData.outboundEntityList ||
          formData.outboundEntityList.length === 0
        ) {
          ElMessage.error('请选择入库商品')
          return
        }

        // 验证入库数量
        const hasValidQuantity = formData.outboundEntityList.some(
          (item: any) => {
            const nums = item.changeUnitNums || item.unitNums || []
            return nums.some((num: any) => num > 0)
          }
        )

        if (!hasValidQuantity) {
          ElMessage.error('请填写入库数量')
          return
        }

        // 准备确认弹窗数据
        confirmDialogData.value = {
          phoneNumber: formData.phone || '',
          remarks: formData.mark || '',
          products: formData.outboundEntityList,
        }

        // 显示确认弹窗
        showConfirmDialog.value = true
      }
    } catch (e: any) {
      ElMessage.error(e.message || '验证失败')
    }
  }

  // 确认弹窗确认处理
  const handleConfirmSubmit = async () => {
    try {
      await skuInfoRef.value.onSubmit2()
      // 注意：不在这里清空数据，等待 handleSubmitSuccess 被调用
    } catch (e: any) {
      ElMessage.error(e.message)
      // 提交失败时不清空数据，不返回第一页，让用户可以修改后重新提交
    }
  }

  // 确认弹窗取消处理
  const handleConfirmCancel = () => {
    showConfirmDialog.value = false
  }

  // 处理提交成功的回调
  const handleSubmitSuccess = () => {
    // 提交成功后跳转到第三页（完成页面）
    activeIndex.value = 2
  }

  // 开始新的入库流程
  const startNewProcess = () => {
    // 清空所有数据并返回第一页
    clearAllData()
  }

  // 处理删除项目的回调
  const handleRemoveItem = (removedItem: any) => {
    // 从 handleSkuList 中移除
    const skuIndex = handleSkuList.value.findIndex(
      item => item.id === removedItem.id
    )
    if (skuIndex > -1) {
      handleSkuList.value.splice(skuIndex, 1)
    }

    // 从 allSelectedData 中移除
    const allIndex = allSelectedData.value.findIndex(
      item => item.id === removedItem.id
    )
    if (allIndex > -1) {
      allSelectedData.value.splice(allIndex, 1)
    }

    // 无论在哪一步，都要更新第一步表格的选中状态
    // 因为用户可能会返回第一步查看
    nextTick(() => {
      // 如果当前在第一步，直接更新选中状态
      if (activeIndex.value === 0) {
        autoSelectRowsFromCache()
      } else {
        // 如果不在第一步，更新缓存的选中状态
        // 当用户返回第一步时会自动应用这些状态
        const elTable =
          productTableRef.value?.table?.table || productTableRef.value?.table
        if (elTable && elTable.toggleRowSelection) {
          const tableList = elTable.getSelectionRows()
          // 查找要取消选中的行
          const rowToDeselect = tableList.find(
            (row: any) => row.id === removedItem.id
          )
          if (rowToDeselect) {
            elTable.toggleRowSelection(rowToDeselect, false)
          }
        }
      }
    })
  }

  // 清空所有数据的函数，只在提交成功后调用
  const clearAllData = () => {
    handleSkuList.value = []
    allSelectedData.value = []
    activeIndex.value = 0

    // 清空表格的选中状态
    // 使用 nextTick 确保在 DOM 更新后执行
    nextTick(() => {
      // 尝试多种方式访问表格实例
      let elTable = null

      if (productTableRef.value?.table?.table) {
        elTable = productTableRef.value.table.table
      } else if (productTableRef.value?.table) {
        elTable = productTableRef.value.table
      } else if (productTableRef.value?.$refs?.table) {
        elTable = productTableRef.value.$refs.table
      }

      if (elTable && elTable.clearSelection) {
        elTable.clearSelection()
      }
    })
  }
  const getTabsHeader = () => {
    /*getTabsHearder().then(res => {
          tabsList.value = res
          getTableData(true)
      })*/
    getTableData(true)
  }
  //获取tab栏
  getTabsHeader()
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }

  .picture {
    width: 30px;
    height: 30px;
  }

  .flex-center {
    margin-left: 80px;
  }

  .success-page {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 500px;
    padding: 40px 20px;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  }

  .success-container {
    width: 100%;
    max-width: 600px;
  }

  .success-card {
    background: #ffffff;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 48px 40px;
    text-align: center;
    position: relative;
    overflow: hidden;
  }

  .success-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #67c23a 0%, #85ce61 100%);
  }

  .success-icon-wrapper {
    margin-bottom: 32px;
  }

  .success-icon-bg {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    box-shadow: 0 8px 24px rgba(103, 194, 58, 0.3);
    animation: pulse 2s infinite;
  }

  @keyframes pulse {
    0% {
      transform: scale(1);
      box-shadow: 0 8px 24px rgba(103, 194, 58, 0.3);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 12px 32px rgba(103, 194, 58, 0.4);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 8px 24px rgba(103, 194, 58, 0.3);
    }
  }

  .success-icon {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .success-content {
    margin-bottom: 40px;
  }

  .success-title {
    font-size: 28px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
  }

  .success-subtitle {
    font-size: 16px;
    color: #7f8c8d;
    margin-bottom: 32px;
    line-height: 1.5;
  }

  .success-details {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 24px;
    margin: 0 auto;
    max-width: 400px;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
  }

  .detail-item:not(:last-child) {
    border-bottom: 1px solid #e9ecef;
  }

  .detail-label {
    font-size: 14px;
    color: #6c757d;
    font-weight: 500;
  }

  .detail-value {
    font-size: 14px;
    color: #495057;
    font-weight: 600;
  }

  .status-processing {
    color: #17a2b8;
    background: #d1ecf1;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 12px;
  }

  .success-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    flex-wrap: wrap;
  }

  .action-button {
    min-width: 180px;
    height: 48px;
    border-radius: 24px;
    font-weight: 500;
    font-size: 14px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  .primary-button {
    background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
    border: none;
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  }

  .primary-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(64, 158, 255, 0.4);
  }

  .secondary-button {
    background: #ffffff;
    border: 2px solid #e9ecef;
    color: #6c757d;
  }

  .secondary-button:hover {
    border-color: #409eff;
    color: #409eff;
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.2);
  }

  .button-icon {
    font-size: 16px;
    font-weight: bold;
  }
</style>
