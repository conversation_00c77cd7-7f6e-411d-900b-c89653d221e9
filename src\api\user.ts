import request from '@/utils/system/request'
import type {
  LoginApiParams,
  LoginApiRes,
  GetInfoApiRes,
  GetCodeRes,
  GetMenuRes,
} from '@/type/user.type'
import type { ResponseList, ResponseMessage } from '@/type/index.type'
/**@name  登录api */
export function loginApi(data: LoginApiParams): Promise<LoginApiRes> {
  return request({
    url: '/api/admin/login',
    method: 'post',
    data,
  })
}

/**@name 获取用户信息Api */
export function getInfoApi(): Promise<GetInfoApiRes> {
  return request({
    url: '/api/admin/getAdminInfoByToken',
    method: 'get',
  })
}

/**@name  退出登录Api */
export function loginOutApi(): Promise<ResponseMessage> {
  return request({
    url: '/api/admin/logout',
    method: 'get',
  })
}
/**@name  获取登录验证码 */
export function getCode(): Promise<GetCodeRes> {
  return request({
    url: '/api/admin/validate/code/get',
    method: 'get',
  })
}
/**@name  获取菜单 */
export function getMenus(): Promise<GetMenuRes[]> {
  return request({
    url: '/api/admin/getMenus',
    method: 'get',
  })
}

export function getImageUrl() {
  return request({
    url: 'api/admin/config/image/prefix',
    method: 'get',
  })
}

/**@name 同步权限菜单 */
export function updateMenuApi(): Promise<ResponseMessage> {
  return request({
    url: 'api/admin/system/config/updateMenu',
    method: 'post',
  })
}
