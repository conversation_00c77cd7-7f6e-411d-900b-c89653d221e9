<template>
  <div class="layout-container" style="padding: 15px">
    <el-tabs v-model="query.type" class="demo-tabs" @tab-click="clickTab">
      <el-tab-pane
        :label="item.name + `(${item.count})`"
        :name="item.type"
        v-for="item in tabsList"
        :key="item.type"
      ></el-tab-pane>
    </el-tabs>
    <div style="text-align: left">
      <el-form label-width="80px" :inline="false">
        <el-form-item label="时间">
          <timePicker
            v-model="query.dateLimit"
            @change="getTableData(true)"
          ></timePicker>
        </el-form-item>
        <el-form-item label-width="0">
          <el-form-item label="操作类型">
            <el-select
              v-model="query.type"
              clearable
              @change="getTableData(true)"
              class="inputWidth"
            >
              <el-option label="入库" :value="1" />
              <el-option label="出库" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item label="商品搜索">
            <div
              class="layout-container-form-search"
              style="align-items: center"
            >
              <!--<el-input v-model="query.keywords" placeholder="请输入关键字" clearable
                        @clear="getTableData(true)"></el-input>-->
              <el-button
                v-if="!dynamicTags.length"
                :icon="Search"
                @click="handleOpen"
              >
                选择商品
              </el-button>
              <el-tag
                closable
                v-for="tag in dynamicTags"
                :key="tag"
                v-if="dynamicTags.length"
                @close="handleClose(tag)"
              >
                商品名称：{{ tag }}
              </el-tag>
            </div>
          </el-form-item>
          <el-form-item label="规格选择" v-if="dynamicTags.length">
            <div
              class="layout-container-form-search"
              style="align-items: center"
            >
              <el-button
                v-if="!dynamicSkuTags.length"
                :icon="Search"
                @click="handleSkuOpen"
              >
                选择规格
              </el-button>
              <el-tag
                closable
                v-for="tag in dynamicSkuTags"
                :key="tag"
                v-if="dynamicSkuTags.length"
                @close="handleSkuClose(tag)"
              >
                规格名称：{{ tag }}
              </el-tag>
            </div>
          </el-form-item>
        </el-form-item>
        <!--<el-form-item label="商品标识">
            <el-checkbox v-model="query.isHot" label="热卖" />
        </el-form-item>-->
      </el-form>
    </div>
		<div class="layout-container-form flex space-between">
      <el-button
          type="success"
          :loading="exportLoading"
          @click="handleExport"
        >
          导出Excel
      </el-button>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column prop="id" label="id" align="center" width="80" />
        <el-table-column prop="productName" label="产品名称" align="center" />
        <el-table-column prop="suk" label="产品规格" align="center" />
        <el-table-column label="类型" align="center">
          <template #default="scope">
            {{ scope.row.type === 1 ? '入库' : '出库' }}
          </template>
        </el-table-column>
        <el-table-column prop="beforeStock" label="修改前" align="center" />
        <el-table-column prop="afterStock" label="修改后" align="center" />
        <el-table-column prop="changeStock" label="修改数量值" align="center" />
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column prop="price" label="金额（元）" align="center" />
        <el-table-column prop="mark" label="单位" align="center" />
        <el-table-column prop="createBy" label="操作人" align="center" />
      </Table>
      <Layer :layer="layer" @getTableData="getSelectPrize" v-if="layer.show" />
      <selectSkuPrize
        :layer="skuLayer"
        @getTableData="getSelectSkuPrize"
        v-if="skuLayer.show"
      />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { defineComponent, ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import { LayerInterface } from '@/components/layer/index.vue'
  import timePicker from '@/components/timePicker/index.vue'
  import { ElMessage } from 'element-plus'
  import { deleteProduct, getStockList, exportProductStock } from '@/api/product'
  import { treeCategroy } from '@/api/categoryApi'
  import Table from '@/components/table/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import { Plus, Delete, Search } from '@element-plus/icons-vue'
  import { exportDataWithLoading, createExportConfig, exportDebounce } from '@/utils/exportUtils'
  import type { TabsPaneContext } from 'element-plus'
  import type { ProductHeaderRes } from '@/type/product.type'
  import { useRouter } from 'vue-router'
  import { useStore } from 'vuex'
  import { TreeCategroyRes } from '@/type/category.type'
  import type { ProductListParams, ProductListRes } from '@/type/product.type'
  import Layer from '@/views/main/product/manage/selectPrize.vue'
  import selectSkuPrize from '@/views/main/product/manage/selectSkuPrize.vue'
  const router = useRouter()
  const store = useStore()

  const dynamicTags = ref([])
  const dynamicSkuTags = ref([])
  const exportLoading = ref(false)
  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
    cateId: '',
    productId: null as number | null,
    skuId: null as number | null,
    type: null as number | null,
    dateLimit: '' as string,
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增商品',
    showButton: true,
  })
  const skuLayer: LayerInterface = reactive({
    show: false,
    title: '新增商品',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<ProductListRes[]>([])

  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: ProductListParams = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getStockList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  // 删除功能
  const handleDel = (data: number) => {
    deleteProduct(data).then(res => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  // 新增弹窗功能
  const handleAdd = () => {
    // layer.title = "新增数据";
    // layer.show = true;
    // delete layer.row;
    router.push({ path: '/product/createproduct' })
  }
  // 编辑弹窗功能
  const handleEdit = (id: number) => {
    router.push({ path: '/product/createproduct', query: { data: id } })
  }

  const handleEditWarehouse = (id: number) => {
    router.push({ path: '/product/editWarehouse', query: { data: id } })
  }

  const tabsList = ref<ProductHeaderRes[]>([])

  //切换tab
  const clickTab = (tab: TabsPaneContext, event: Event) => {
    getTableData(true)
  }
  //商品分类选择框
  const options = ref<TreeCategroyRes[]>([])
  const props = {
    value: 'id',
    label: 'name',
    children: 'child',
    emitPath: false,
  }
  treeCategroy({ type: 1, status: -1 }).then(res => [(options.value = res)])

  const handleClose = (tag: string) => {
    dynamicTags.value.splice((dynamicTags.value as string[]).indexOf(tag), 1)
    dynamicSkuTags.value.splice(
      (dynamicSkuTags.value as string[]).indexOf(tag),
      1
    )
    query.productId = null
    query.skuId = null
    getTableData(true)
  }

  const handleSkuClose = (tag: string) => {
    dynamicSkuTags.value.splice(
      (dynamicSkuTags.value as string[]).indexOf(tag),
      1
    )
    query.skuId = null
    getTableData(true)
  }

  const handleOpen = () => {
    layer.title = '选择商品'
    layer.show = true
  }

  const handleSkuOpen = () => {
    skuLayer.title = '选择sku'
    skuLayer.show = true
    skuLayer.id = query.productId
  }

  const getSelectPrize = (info: any) => {
    query.productId = info.id as number
    ;(dynamicTags.value as string[]).push(info.productName)
    getTableData(true)
  }

  const getSelectSkuPrize = (info: any) => {
    console.log(info, 'info')
    query.skuId = info.id as number
    ;(dynamicSkuTags.value as string[]).push(info.suk)
    getTableData(true)
  }

  //恢复商品
  const getTabsHeader = () => {
    /*getTabsHearder().then(res => {
          tabsList.value = res
          getTableData(true)
      })*/
    getTableData(true)
  }
  // 原始导出函数
  const originalHandleExport = async () => {
    // 准备导出参数
    const exportParams: any = {}

    if (query.productId && query.productId !== 0) {
      exportParams.productId = query.productId
    }

    if (query.skuId && query.skuId !== 0) {
      exportParams.skuId = query.skuId
    }

    if (query.type && query.type !== 0) {
      exportParams.type = query.type
    }

    if (query.dateLimit && query.dateLimit.trim() !== '') {
      exportParams.dateLimit = query.dateLimit
    }

    // 使用通用导出函数
    const config = createExportConfig.productStock(exportParams)
    await exportDataWithLoading(config, exportLoading)
  }

  // 使用防抖包装的导出函数 - 1秒防抖，2秒最小间隔
  const handleExport = exportDebounce(originalHandleExport, { delay: 500, minInterval: 1000 })

  //获取tab栏
  getTabsHeader()
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }

  .picture {
    width: 30px;
    height: 30px;
  }
</style>
