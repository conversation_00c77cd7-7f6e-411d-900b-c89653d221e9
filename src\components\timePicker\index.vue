<template>
  <el-radio-group v-model="dateLimit" @change="onChange">
    <el-radio-button
      v-for="(item, index) in fromList.fromTxt"
      :key="index"
      :label="item.val"
    >
      {{ item.text }}
    </el-radio-button>
  </el-radio-group>
  <div style="width: 250px">
    <el-date-picker
      v-model="timeVal"
      value-format="YYYY-MM-DD"
      format="YYYY-MM-DD"
      type="daterange"
      placement="bottom-end"
      placeholder="自定义时间"
      @change="onchangeTime"
    />
  </div>
</template>

<script lang="ts" setup>
  import { fromList } from '@/utils/contants'
  import { ref, defineProps, defineEmits, watch } from 'vue'
  const props = defineProps({
    modelValue: {
      tpye: String,
      default: '',
    },
  })
  const emit = defineEmits(['update:modelValue', 'change'])
  const timeVal = ref([])
  const dateLimit = ref('')
  watch(props, newval => {
    dateLimit.value = props.modelValue
  })
  const onchangeTime = (e: []) => {
    timeVal.value = e
    dateLimit.value = e ? timeVal.value.join(',') : ''
    console.log(dateLimit.value)
    onChange()
  }
  const onChange = () => {
    emit('update:modelValue', dateLimit.value)
    emit('change', 'change')
  }
</script>
