<template>
  <div
    v-if="visible"
    class="fixed inset-0 z-50 flex items-center justify-center bg-black/50"
    @click="handleCancel"
  >
    <div
      class="w-full max-w-[40rem] rounded-lg bg-white shadow-xl dialog-container"
      @click.stop
    >
      <!-- Header -->
      <div class="relative header-section px-4 py-3">
        <button class="close-button" @click="handleCancel">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
          >
            <line x1="18" y1="6" x2="6" y2="18"></line>
            <line x1="6" y1="6" x2="18" y2="18"></line>
          </svg>
        </button>
        <h2 class="text-center text-lg font-medium page-title">库存管理</h2>
        <p class="mt-1 text-center text-sm page-tip">
          请选择操作类型并填写相关信息
        </p>
      </div>

      <!-- Material Info -->
      <div class="material-info-section px-4 py-3">
        <h3 class="mb-3 text-center text-base font-medium page-title">原料信息</h3>
        <div class="space-y-2">
          <div class="flex justify-between">
            <span class="page-tip">原料名称:</span>
            <span class="font-medium page-title">{{ materialData.rawMaterialName }}</span>
          </div>
          <!-- 多单位库存显示 -->
          <div v-if="isMultiUnit" class="space-y-2">
            <div class="flex justify-between">
              <span class="page-tip">当前库存:</span>
              <span class="font-medium stock-highlight"></span>
            </div>
            <!-- 网格布局显示库存信息，每行3个 -->
            <div class="grid grid-cols-3 gap-2 text-sm">
              <div v-for="(unitName, index) in materialData.unitNames" :key="index" class="flex flex-col items-center p-2 bg-gray-50 rounded">
                <span class="page-tip text-xs">{{ unitName }}</span>
                <span class="font-medium stock-highlight">{{ materialData.unitNums?.[index] || 0 }}</span>
              </div>
            </div>
          </div>
          <!-- 单一单位库存显示 -->
          <div v-else>
            <div class="flex justify-between">
              <span class="page-tip">当前库存:</span>
              <span class="font-medium stock-highlight">
                {{ materialData.rawMaterialNum }} {{ materialData.unit }}
              </span>
            </div>
            <div class="flex justify-between">
              <span class="page-tip">单位:</span>
              <span class="font-medium page-title">{{ materialData.unit }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabs -->
      <div class="px-4 py-3 tabs-section">
        <el-tabs v-model="activeTab" type="border-card" class="custom-tabs">
          <el-tab-pane label="📦 入库" name="1"></el-tab-pane>
          <el-tab-pane label="📤 出库" name="2"></el-tab-pane>
        </el-tabs>
      </div>

      <!-- Content -->
      <div class="px-4 py-3">
        <h3 class="mb-3 text-base font-medium">
          {{ currentOperationType }}信息
        </h3>

        <!-- 多单位表格 -->
        <div v-if="isMultiUnit" class="multi-unit-table">
          <el-table :data="unitTableData" size="small" border>
            <el-table-column label="单位" align="center" width="80">
              <template #default="scope">
                {{ scope.row.unitName }}
              </template>
            </el-table-column>
            <el-table-column label="当前库存" align="center" width="100">
              <template #default="scope">
                {{ scope.row.currentStock }}
              </template>
            </el-table-column>
            <el-table-column :label="currentOperationType + '数量'" align="center">
              <template #default="scope">
                <el-input-number
                  v-model="scope.row.operationNum"
                  :min="0"
                  :max="activeTab === '2' ? scope.row.currentStock : undefined"
                  :precision="0"
                  controls-position="right"
                  size="small"
                  style="width: 100%"
                  @change="handleUnitNumChange"
                />
              </template>
            </el-table-column>
          </el-table>
          <p class="mt-2 text-xs page-tip text-center">
            {{ activeTab === '1' ? '请输入各单位的入库数量（正整数）' : '请输入各单位的出库数量，不能超过当前库存' }}
          </p>
        </div>

        <!-- 单一单位输入（兼容模式） -->
        <div v-else class="mb-3">
          <div class="flex items-center justify-between">
            <label class="text-sm font-medium">
              <span class="text-red-500">*</span>
              数量
            </label>
            <div class="flex items-center">
              <div class="quantity-input-group">
                <button
                  type="button"
                  @click="handleDecrement"
                  :disabled="form.num <= 1"
                  class="quantity-btn quantity-btn-left"
                >
                  −
                </button>
                <input
                  type="number"
                  v-model.number="form.num"
                  @input="handleNumberInput"
                  class="quantity-input"
                  :min="1"
                  step="1"
                />
                <button
                  type="button"
                  @click="handleIncrement"
                  :disabled="activeTab === '2' && form.num >= materialData.rawMaterialNum"
                  class="quantity-btn quantity-btn-right"
                >
                  +
                </button>
              </div>
              <span class="ml-3 text-sm text-gray-600">
                {{ materialData.unit }}
              </span>
            </div>
          </div>
          <p class="mt-2 text-xs page-tip text-right">
            {{
              activeTab === '1'
                ? '请输入正整数数量'
                : `请输入不超过当前库存(${materialData.rawMaterialNum})的正整数数量`
            }}
          </p>
        </div>
      </div>

      <!-- Footer -->
      <div class="footer-section flex justify-end gap-3 px-4 py-3">
        <button
          @click="handleCancel"
          class="cancel-btn"
        >
          取消
        </button>
        <button
          @click="handleConfirm"
          :disabled="loading"
          class="confirm-btn"
        >
          <span
            v-if="loading"
            class="loading-spinner"
          ></span>
          确认{{ currentOperationType }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue'
  import { ElMessage, ElMessageBox, ElTabs, ElTabPane, ElTable, ElTableColumn, ElInputNumber } from 'element-plus'
  import type { MaterialListRes } from '@/type/material.type'

  // 单位表格数据接口
  interface UnitTableRow {
    unitName: string
    currentStock: number
    operationNum: number
  }

  // 定义 props
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    materialData: {
      type: Object as () => MaterialListRes,
      required: true,
      default: () => ({
        id: 0,
        cateId: '',
        rawMaterialName: '',
        image: '',
        rawMaterialNum: 0,
        unit: '',
      }),
    },
  })

  // 定义 emits
  const emit = defineEmits(['update:visible', 'confirm', 'cancel'])

  // 当前选中的选项卡
  const activeTab = ref('1') // 默认选中入库

  // 表单数据
  const form = ref({
    num: 1,
  })

  // 多单位表格数据
  const unitTableData = ref<UnitTableRow[]>([])

  // 加载状态
  const loading = ref(false)

  // 计算属性
  const currentOperationType = computed(() => {
    return activeTab.value === '1' ? '入库' : '出库'
  })

  // 判断是否为多单位模式
  const isMultiUnit = computed(() => {
    return props.materialData.unitNames &&
           props.materialData.unitNames.length > 0 &&
           props.materialData.unitNums &&
           props.materialData.unitNums.length > 0
  })

  // 初始化多单位表格数据
  const initUnitTableData = () => {
    if (isMultiUnit.value) {
      unitTableData.value = props.materialData.unitNames!.map((unitName, index) => ({
        unitName,
        currentStock: props.materialData.unitNums?.[index] || 0,
        operationNum: 0
      }))
    }
  }

  // 监听弹窗显示状态，重置表单
  watch(
    () => props.visible,
    newVal => {
      if (newVal) {
        activeTab.value = '1' // 重置为入库选项卡
        form.value.num = 1
        initUnitTableData() // 初始化多单位表格数据
      }
    }
  )

  // 监听选项卡切换
  watch(activeTab, () => {
    form.value.num = 1 // 重置数量
    // 重置多单位操作数量
    unitTableData.value.forEach(row => {
      row.operationNum = 0
    })
  })

  // 处理单位数量变化
  const handleUnitNumChange = () => {
    // 可以在这里添加额外的验证逻辑
  }

  // 处理数量增加
  const handleIncrement = () => {
    if (activeTab.value === '2') {
      // 出库时检查库存限制
      if (form.value.num < props.materialData.rawMaterialNum) {
        form.value.num += 1
      }
    } else {
      // 入库时无最大值限制
      form.value.num += 1
    }
  }

  // 处理数量减少
  const handleDecrement = () => {
    if (form.value.num > 1) {
      form.value.num -= 1
    }
  }

  // 处理数字输入
  const handleNumberInput = (event: Event) => {
    const target = event.target as HTMLInputElement
    let value = parseInt(target.value) || 1

    // 确保值在有效范围内
    const minValue = 1

    if (value < minValue) {
      value = minValue
    } else if (activeTab.value === '2' && value > props.materialData.rawMaterialNum) {
      // 出库时检查库存限制
      value = props.materialData.rawMaterialNum
    }
    // 入库时不设置最大值限制

    form.value.num = value
    target.value = value.toString()
  }

  // 处理取消
  const handleCancel = () => {
    emit('update:visible', false)
    emit('cancel')
  }

  // 处理确认 - 显示二次确认对话框
  const handleConfirm = async () => {
    try {
      // 先进行基本验证
      let data: any = {
        rawMaterialId: props.materialData.id,
        type: parseInt(activeTab.value), // 使用当前选中的选项卡值
      }

      if (isMultiUnit.value) {
        // 多单位模式
        const nums = unitTableData.value.map(row => row.operationNum)

        // 验证是否有输入数量
        const hasValidInput = nums.some(num => num > 0)
        if (!hasValidInput) {
          ElMessage.error('请至少输入一个单位的数量')
          return
        }

        // 出库时检查库存
        if (activeTab.value === '2') {
          for (let i = 0; i < unitTableData.value.length; i++) {
            const row = unitTableData.value[i]
            if (row.operationNum > row.currentStock) {
              ElMessage.error(`${row.unitName}的出库数量不能大于当前库存`)
              return
            }
          }
        }

        data.nums = nums
      } else {
        // 单一单位模式（兼容）
        if (form.value.num < 1) {
          ElMessage.error('数量必须大于0')
          return
        }

        // 出库时检查库存
        if (
          activeTab.value === '2' &&
          form.value.num > props.materialData.rawMaterialNum
        ) {
          ElMessage.error('出库数量不能大于当前库存')
          return
        }

        data.num = form.value.num
      }

      // 显示二次确认对话框
      await showConfirmDialog(data)
    } catch (error) {
      console.error('操作失败:', error)
      ElMessage.error('操作失败，请重试')
    }
  }

  // 显示确认对话框
  const showConfirmDialog = async (data: any) => {
    const operationType = activeTab.value === '1' ? '入库' : '出库'

    // 创建确认对话框的表格内容
    const createConfirmContent = () => {
      let htmlContent = `
        <div style="padding: 10px 0;">
          <div style="margin-bottom: 15px; font-size: 16px; font-weight: 500; text-align: center;">
            确认要进行${operationType}操作吗？
          </div>
      `

      if (isMultiUnit.value) {
        // 多单位模式 - 创建表格显示各单位数量
        const validUnits = unitTableData.value.filter(row => row.operationNum > 0)

        htmlContent += `
          <div style="margin-bottom: 10px;">
            <span style="font-weight: 500;">原料名称：</span>
            <span>${props.materialData.rawMaterialName}</span>
          </div>
          <div style="margin-bottom: 10px; font-weight: 500;">${operationType}数量：</div>
          <table style="width: 350px; border-collapse: collapse; border: 1px solid #dcdfe6; font-size: 14px;">
            <thead>
              <tr style="background-color: #f5f7fa;">
                <th style="width: 40%; border: 1px solid #dcdfe6; padding: 8px; text-align: center; font-weight: 600;">单位</th>
                <th style="width: 60%; border: 1px solid #dcdfe6; padding: 8px; text-align: center; font-weight: 600;">${operationType}数量</th>
              </tr>
            </thead>
            <tbody>
        `

        validUnits.forEach(row => {
          htmlContent += `
            <tr>
              <td style="border: 1px solid #dcdfe6; padding: 8px; text-align: center;">${row.unitName}</td>
              <td style="border: 1px solid #dcdfe6; padding: 8px; text-align: center; color: #409eff; font-weight: 500;">${row.operationNum}</td>
            </tr>
          `
        })

        htmlContent += `
            </tbody>
          </table>
        `
      } else {
        // 单一单位模式 - 创建简单表格
        htmlContent += `
          <table style="width: 100%; border-collapse: collapse; border: 1px solid #dcdfe6; font-size: 14px;">
            <tbody>
              <tr>
                <td style="border: 1px solid #dcdfe6; padding: 12px; text-align: right; background-color: #f5f7fa; font-weight: 500; color: #606266; width: 120px;">原料名称：</td>
                <td style="border: 1px solid #dcdfe6; padding: 12px; color: #303133;">${props.materialData.rawMaterialName}</td>
              </tr>
              <tr>
                <td style="border: 1px solid #dcdfe6; padding: 12px; text-align: right; background-color: #f5f7fa; font-weight: 500; color: #606266;">操作类型：</td>
                <td style="border: 1px solid #dcdfe6; padding: 12px; color: #303133;">${operationType}</td>
              </tr>
              <tr>
                <td style="border: 1px solid #dcdfe6; padding: 12px; text-align: right; background-color: #f5f7fa; font-weight: 500; color: #606266;">${operationType}数量：</td>
                <td style="border: 1px solid #dcdfe6; padding: 12px; color: #409eff; font-weight: 500;">${data.num} ${props.materialData.unit}</td>
              </tr>
            </tbody>
          </table>
        `
      }

      htmlContent += `</div>`
      return htmlContent
    }

    try {
      await ElMessageBox.confirm(
        createConfirmContent(),
        `${operationType}确认`,
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
        }
      )

      // 用户确认后执行实际操作
      await executeOperation(data)
    } catch {
      // 用户取消，不做任何操作
    }
  }

  // 执行实际的库存操作
  const executeOperation = async (data: any) => {
    try {
      loading.value = true
      emit('confirm', data)
    } catch (error) {
      console.error('操作失败:', error)
      ElMessage.error('操作失败，请重试')
    } finally {
      loading.value = false
    }
  }
</script>

<style scoped>
  /* 使用主题色变量 */
  .page-title {
    color: var(--system-page-color);
  }

  .page-tip {
    color: var(--system-page-tip-color);
  }

  .stock-highlight {
    color: var(--system-primary-color);
  }

  /* 分割线样式 - 使用更轻的边框 */
  .header-section {
    border-bottom: 1px solid var(--system-page-border-color);
  }

  .material-info-section {
    border-bottom: 1px solid var(--system-page-border-color);
  }

  .tabs-section {
    border-bottom: 1px solid var(--system-page-border-color);
  }

  .footer-section {
    border-top: 1px solid var(--system-page-border-color);
  }

  /* 数量输入组样式 */
  .quantity-input-group {
    display: flex;
    border-radius: 6px;
    background: var(--system-page-background);
    border: 1px solid var(--system-page-border-color);
    overflow: hidden;
    transition: all 0.2s ease;
  }

  .quantity-input-group:focus-within {
    border-color: var(--system-primary-color);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  }

  .quantity-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: #f8fafc;
    color: var(--system-page-tip-color);
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 16px;
    font-weight: 500;
  }

  .quantity-btn:hover:not(:disabled) {
    background: var(--system-primary-color);
    color: var(--system-primary-text-color);
  }

  .quantity-btn:disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background: #f1f5f9;
  }

  .quantity-btn-left {
    border-right: 1px solid var(--system-page-border-color);
  }

  .quantity-btn-right {
    border-left: 1px solid var(--system-page-border-color);
  }

  .quantity-input {
    width: 4rem;
    height: 2.5rem;
    border: none;
    background: transparent;
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    color: var(--system-page-color);
    outline: none;
  }

  /* 隐藏数字输入框的默认箭头 */
  input[type='number']::-webkit-outer-spin-button,
  input[type='number']::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type='number'] {
    -moz-appearance: textfield;
  }

  /* 按钮样式 */
  .cancel-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    background: var(--system-page-background);
    color: var(--system-page-color);
    border: 1px solid var(--system-page-border-color);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .cancel-btn:hover {
    background: #f8fafc;
    border-color: var(--system-primary-color);
  }

  .confirm-btn {
    padding: 0.5rem 1rem;
    border-radius: 6px;
    background: var(--system-primary-color);
    color: var(--system-primary-text-color);
    border: 1px solid var(--system-primary-color);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .confirm-btn:hover:not(:disabled) {
    background: #3a8ee6;
    border-color: #3a8ee6;
  }

  .confirm-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid var(--system-primary-text-color);
    border-top-color: transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* 自定义 el-tabs 样式 */
  :deep(.custom-tabs) {
    margin: 0;
  }

  :deep(.custom-tabs .el-tabs__header) {
    margin: 0;
    border-bottom: none;
  }

  :deep(.custom-tabs .el-tabs__nav-wrap) {
    padding: 0;
  }

  :deep(.custom-tabs .el-tabs__nav) {
    border: 1px solid var(--system-page-border-color);
    border-radius: 6px;
    background: #f8fafc;
    overflow: hidden;
    width: 100%;
    display: flex;
  }

  :deep(.custom-tabs .el-tabs__item) {
    border: none;
    border-radius: 0;
    font-weight: 600;
    font-size: 16px;
    padding: 8px 16px;
    transition: all 0.3s ease;
    background: transparent;
    color: var(--system-page-tip-color);
    text-align: center;
    height: auto;
    line-height: 1.5;
    flex: 1;
    width: 50%;
    margin: 0;
  }

  :deep(.custom-tabs .el-tabs__item:hover) {
    background: rgba(64, 158, 255, 0.1);
    color: var(--system-primary-color);
  }

  :deep(.custom-tabs .el-tabs__item.is-active) {
    background: var(--system-primary-color);
    color: var(--system-primary-text-color);
    font-weight: 700;
  }

  :deep(.custom-tabs .el-tabs__active-bar) {
    display: none;
  }

  :deep(.custom-tabs .el-tabs__content) {
    padding: 0;
  }

  /* 添加一些微妙的动画效果 */
  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .dialog-container {
    animation: fadeInUp 0.3s ease-out;
  }

  /* 关闭按钮样式 */
  .close-button {
    position: absolute;
    top: 20px;
    right: 20px;
    background: none;
    border: none;
    cursor: pointer;
    color: var(--system-page-tip-color);
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
  }

  .close-button:hover {
    color: var(--system-page-color);
    background: rgba(64, 158, 255, 0.1);
  }

  /* 多单位表格样式 */
  .multi-unit-table {
    margin-bottom: 1rem;
  }

  .multi-unit-table :deep(.el-table) {
    border-radius: 6px;
    overflow: hidden;
  }

  .multi-unit-table :deep(.el-table th) {
    background: #f8fafc;
    color: var(--system-page-color);
    font-weight: 600;
  }

  .multi-unit-table :deep(.el-table td) {
    padding: 8px 0;
  }

  .multi-unit-table :deep(.el-input-number) {
    width: 100%;
  }

  .multi-unit-table :deep(.el-input-number .el-input__inner) {
    text-align: center;
  }
</style>
