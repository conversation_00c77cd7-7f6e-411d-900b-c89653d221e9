
import request from '@/utils/system/request'
import type { 
    ExtractListParams, 
    ExtractListRes, 
    ExtractRejectParams, 
    RecordsListParams, 
    RecordsListRes, 
    MoneyRecordParams, 
    MoneyRecordRes,
    ChannelRes,
    IntegralRecordListParams,
    IntegralRecordListRes
 } from '@/type/financial.type'
import type { ResponseList, ResponseMessage } from '@/type/index.type'
// 提现记录列表
export function getExtractList(data: ExtractListParams): Promise<ResponseList<ExtractListRes[]>> {
    return request({
        url: '/api/admin/extract/list',
        method: 'get',
        params: data
    })
}
//通过提现
export function extractConfirm(data: number | string): Promise<boolean> {
    return request({
        url: '/api/admin/extract/confirm/' + data,
        method: 'post',
    })
}
//驳回提现
export function extractReject(data: ExtractRejectParams): Promise<boolean> {
    return request({
        url: '/api/admin/extract/reject',
        method: 'post',
        data: data
    })
}
//充值记录
export function getRecordsList(data: RecordsListParams): Promise<ResponseList<RecordsListRes[]>> {
    return request({
        url: '/api/admin/finance/recharge/records/list',
        method: 'get',
        params: data
    })
}

/* 余额记录列表 */
export function moneyRecordList(params: MoneyRecordParams): Promise<ResponseList<MoneyRecordRes[]>> {
    return request({
        url: `/api/admin/finance/money/recordList`,
        method: 'get',
        params
    })
}
/* 余额明细类型列表 */
export function moneyChannelList():Promise<ChannelRes> {
    return request({
        url: `/api/admin/finance/money/channelList`,
        method: 'get',
    })
}
/* 积分记录列表 */
export function integralRecordList(params: IntegralRecordListParams):Promise<ResponseList<IntegralRecordListRes[]>> {
    return request({
        url: `/api/admin/finance/integral/recordList`,
        method: 'get',
        params
    })
}
/* 积分明细类型列表 */
export function integralChannelList():Promise<ChannelRes> {
    return request({
        url: `/api/admin/finance/integral/channelList`,
        method: 'get',
    })
}