/**
 * 原料列表请求参数
 */
export interface MaterialListParams {
  /**
   * 原料分类ID
   */
  cateId?: string
  /**
   * 关键字搜索
   */
  keywords?: string
  /**
   * 页码
   */
  page: number
  /**
   * 每页数量
   */
  limit: number
  /**
   * 排序字段
   */
  sortColumn?: string
  /**
   * 排序类型 asc-升序 desc-降序
   */
  sortType?: 'asc' | 'desc'
  /**
   * 额外筛选参数 (如: stockLack-库存不足)
   */
  extra?: string
}

/**
 * 原料列表响应数据
 */
export interface MaterialListRes {
  /**
   * 原料ID
   */
  id: number
  /**
   * 分类ID
   */
  cateId: string
  /**
   * 原料名称
   */
  rawMaterialName: string
  /**
   * 原料图片
   */
  image: string
  /**
   * 原料数量
   */
  rawMaterialNum: number
  /**
   * 单位
   */
  unit: string
  /**
   * 单位组ID（兼容性保留，新API使用unitId）
   */
  unitGroupId?: number
  /**
   * 单位组ID
   */
  unitId?: number
  /**
   * 各单位数量数组
   */
  unitNums?: number[]
  /**
   * 单位组字符串表示（如："8包0件1斤"）
   */
  unitGroupStr?: string
  /**
   * 单位名称数组（如：["包", "件", "斤"]）
   */
  unitNames?: string[]
  /**
   * 警戒库存数量数组（如：[4, 1, 3]）
   */
  storeStockNums?: number[]
  /**
   * 加载状态（前端使用）
   */
  loading?: boolean
}

/**
 * 原料保存/更新参数
 */
export interface MaterialSaveParams {
  /**
   * 原料ID（编辑时需要）
   */
  id?: number
  /**
   * 分类ID
   */
  cateId: string
  /**
   * 原料名称
   */
  rawMaterialName: string
  /**
   * 原料图片
   */
  image?: string
  /**
   * 原料数量
   */
  rawMaterialNum: number
  /**
   * 单位组ID
   */
  unitId?: number
  /**
   * 各单位数量数组
   */
  unitNums?: number[]
  /**
   * 警戒库存数量数组
   */
  storeStockNums?: number[]
}

/**
 * 原料出入库参数
 */
export interface MaterialStockChangeParams {
  /**
   * 原料ID
   */
  rawMaterialId: number
  /**
   * 操作类型 1-入库 2-出库
   */
  type: number
  /**
   * 数量（单一单位模式，兼容性保留）
   */
  num?: number
  /**
   * 各单位数量数组（多单位模式）
   */
  nums?: number[]
}

/**
 * 原料出入库记录列表请求参数
 */
export interface MaterialStockLogParams {
  /**
   * 页码
   */
  page: number
  /**
   * 每页数量
   */
  limit: number
  /**
   * 原料ID，0表示全部
   */
  rawMaterialId?: number
  /**
   * 出入库类型，0全部，1入库，2出库
   */
  type?: number
  /**
   * 时间范围
   */
  dateLimit?: string
}

/**
 * 原料出入库记录列表响应数据
 */
export interface MaterialStockLogRes {
  /**
   * 创建人
   */
  createBy: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新人
   */
  updateBy: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 记录ID
   */
  id: number
  /**
   * 原料ID
   */
  rawMaterialId: number
  /**
   * 原料名称
   */
  rawMaterialName: string
  /**
   * 操作类型 1-入库 2-出库
   */
  type: number
  /**
   * 修改前数量（单一单位模式，兼容性保留）
   */
  beforeNum: number
  /**
   * 修改数量（单一单位模式，兼容性保留）
   */
  num: number
  /**
   * 修改后数量（单一单位模式，兼容性保留）
   */
  afterNum: number
  /**
   * 修改前库存（多单位格式字符串）
   */
  beforeStock: string
  /**
   * 修改库存数量（多单位格式字符串）
   */
  changeStock: string
  /**
   * 修改后库存（多单位格式字符串）
   */
  afterStock: string
  /**
   * 备注信息
   */
  mark: string
}
