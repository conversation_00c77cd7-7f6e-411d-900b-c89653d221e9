const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * 构建脚本 - 支持构建后自动部署
 * 使用方法:
 * yarn build                    # 仅构建
 * yarn build --ip server1       # 构建并部署到server1
 */

class Builder {
	constructor() {
		this.shouldDeploy = false;
		this.serverKey = null;
	}

	/**
	 * 解析命令行参数
	 */
	parseArgs() {
		const args = process.argv.slice(2);

		// 查找 --ip 参数的索引
		const ipIndex = args.findIndex(arg => arg === '--ip');

		if (ipIndex !== -1 && ipIndex + 1 < args.length) {
			// --ip 参数存在且后面有值
			this.shouldDeploy = true;
			this.serverKey = args[ipIndex + 1];
			console.log(`🎯 检测到 --ip 参数: ${this.serverKey}`);
			console.log(`📦 将在构建完成后自动部署到服务器`);
		} else {
			console.log(`📦 开始构建项目...`);
		}
	}

	/**
	 * 验证部署配置
	 */
	validateDeployConfig() {
		if (!this.shouldDeploy) return true;

		const configPath = path.join(process.cwd(), 'proxyConfig.json');

		if (!fs.existsSync(configPath)) {
			console.error('❌ 错误: 找不到 proxyConfig.json 配置文件');
			console.log('💡 提示: 请先复制 proxyConfig.example.json 并配置服务器信息');
			return false;
		}

		try {
			const configData = fs.readFileSync(configPath, 'utf8');
			const allConfigs = JSON.parse(configData);

			if (!allConfigs[this.serverKey]) {
				console.error(`❌ 错误: 在 proxyConfig.json 中找不到 "${this.serverKey}" 的配置`);
				console.log('可用的配置:', Object.keys(allConfigs).join(', '));
				return false;
			}

			console.log(`✅ 部署配置验证通过: ${this.serverKey}`);
			return true;
		} catch (error) {
			console.error('❌ 错误: 解析配置文件失败', error.message);
			return false;
		}
	}

	/**
	 * 执行 vite 构建
	 */
	async runBuild() {
		return new Promise((resolve, reject) => {
			console.log('🔨 开始执行 vite 构建...');

			const buildProcess = spawn('npx', ['vite', 'build', '--mode=production'], {
				stdio: 'inherit',
				shell: true,
			});

			buildProcess.on('close', code => {
				if (code === 0) {
					console.log('✅ 构建完成!');
					resolve();
				} else {
					console.error(`❌ 构建失败，退出码: ${code}`);
					reject(new Error(`Build failed with code ${code}`));
				}
			});

			buildProcess.on('error', error => {
				console.error('❌ 构建过程出错:', error.message);
				reject(error);
			});
		});
	}

	/**
	 * 执行部署
	 */
	async runDeploy() {
		return new Promise((resolve, reject) => {
			console.log(`🚀 开始部署到服务器: ${this.serverKey}`);

			const deployProcess = spawn('node', ['scripts/deploy.js', '--ip', this.serverKey], {
				stdio: 'inherit',
				shell: true,
			});

			deployProcess.on('close', code => {
				if (code === 0) {
					console.log('🎊 部署完成!');
					resolve();
				} else {
					console.error(`❌ 部署失败，退出码: ${code}`);
					reject(new Error(`Deploy failed with code ${code}`));
				}
			});

			deployProcess.on('error', error => {
				console.error('❌ 部署过程出错:', error.message);
				reject(error);
			});
		});
	}

	/**
	 * 主流程
	 */
	async run() {
		try {
			// 1. 解析参数
			this.parseArgs();

			// 2. 如果需要部署，先验证配置
			if (this.shouldDeploy && !this.validateDeployConfig()) {
				process.exit(1);
			}

			// 3. 执行构建
			await this.runBuild();

			// 4. 如果指定了 --ip 参数，执行部署
			if (this.shouldDeploy) {
				console.log(''); // 空行分隔
				await this.runDeploy();

				console.log('');
				console.log('🎉 构建和部署全部完成!');
				console.log(`🌐 您的应用已成功部署到: ${this.serverKey}`);
			} else {
				console.log('');
				console.log('🎉 构建完成!');
				console.log('💡 提示: 使用 yarn build --ip 服务器名 可以构建后自动部署');
			}
		} catch (error) {
			console.error('💥 操作失败:', error.message);
			process.exit(1);
		}
	}
}

// 运行构建器
const builder = new Builder();
builder.run();
