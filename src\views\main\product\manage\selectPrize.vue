<template>
  <Layer :layer="layer" @confirm="submit()" ref="layerDom">
    <el-form label-position="right" label-width="100px">
      <el-row>
        <el-form-item label="商品搜索">
          <div class="layout-container-form-search">
            <el-input
              v-model="query.keywords"
              placeholder="请输入商品名称"
              clearable
              @clear="getTableData(true)"
            ></el-input>
            <el-button
              type="primary"
              :icon="Search"
              class="search-btn"
              @click="getTableData(true)"
            >
              {{ $t('message.common.search') }}
            </el-button>
          </div>
        </el-form-item>
      </el-row>
    </el-form>
    <Table
      ref="table"
      v-model:page="page"
      v-loading="loading"
      :data="tableData"
      @getTableData="getTableData"
      style="height: 500px"
    >
      <el-table-column label="#" align="center" width="80">
        <template #default="scope">
          <div>
            <el-radio
              @change="handSelectProduct(scope.row)"
              v-model="scope.row.productRadio"
            ></el-radio>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="id" label="ID" align="center" width="80" />
      <el-table-column label="图片" align="center">
        <template #default="scope">
          <imagePreview :imgurl="scope.row.image" />
        </template>
      </el-table-column>
      <el-table-column prop="productName" label="商品名称" align="center" />
    </Table>
  </Layer>
</template>

<script lang="ts" setup>
  import type { LayerType } from '@/components/layer/index.vue'
  import type { Ref } from 'vue'
  import { defineComponent, ref, reactive } from 'vue'
  import Layer from '@/components/layer/index.vue'
  import { Search } from '@element-plus/icons-vue'
  import { Page } from '@/components/table/type'
  import Table from '@/components/table/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import { getProductList } from '@/api/product'
  import { ProductListRes } from '@/type/product.type'
  const props = defineProps({
    layer: {
      type: Object,
      default: () => {
        return {
          show: false,
          title: '',
          showButton: true,
        }
      },
    },
  })
  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
    cateId: '',
    productName: '',
    type: 1,
  })
  const tableData = ref<ProductListRes[]>([])
  const loading = ref(true)
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getProductList(params)
      .then(res => {
        let data = res.list
        data.forEach((d: any) => {
          d.loading = false
          d.productRadio = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  getTableData(true)
  const emit = defineEmits(['getTableData'])
  const layerDom: Ref<LayerType | null> = ref(null)
  const submit = () => {
    emit('getTableData', chooseList.value)
    layerDom.value && layerDom.value.close()
  }
  const chooseList = ref([])
  const handSelectProduct = (data: any) => {
    chooseList.value = data
    emit('getTableData', chooseList.value)
    layerDom.value && layerDom.value.close()
  }
</script>

<style lang="scss" scoped></style>
