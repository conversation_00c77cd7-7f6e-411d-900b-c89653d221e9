{"name": "vue-admin-box", "version": "1.1.0", "scripts": {"dev": "vite", "start": "vite", "build": "node scripts/build.js", "build:stag": "vite build --mode=staging", "serve": "vite preview", "format": "prettier --write .", "format:check": "prettier --check .", "lint:fix": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix && prettier --write ."}, "dependencies": {"@form-create/element-ui": "^3.1.18", "@kangc/v-md-editor": "^2.3.5", "@unocss/preset-wind3": "^66.1.3", "@vitejs/plugin-vue-jsx": "^2.1.1", "@vueuse/core": "^8.0.0", "axios": "^0.26.1", "codemirror": "^5.65.2", "cropperjs": "^1.5.12", "echarts": "^5.3.1", "element-plus": "^2.10.1", "he-tree-vue": "^3.1.2", "lucide-vue-next": "^0.525.0", "mockjs": "^1.1.0", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "print-js": "^1.6.0", "sortablejs": "^1.15.6", "splitpanes": "^3.1.1", "throttle-debounce": "^3.0.1", "v-contextmenu": "^3.0.0", "vform3-builds": "^3.0.10", "vue": "^3.2.31", "vue-i18n": "9.1.10", "vue-router": "4", "vuedraggable": "^4.1.0", "vuex": "^4.0.2", "xlsx": "^0.18.5", "zxing-wasm": "^2.2.0"}, "devDependencies": {"@types/node": "^17.0.21", "@types/sortablejs": "^1.15.8", "@vitejs/plugin-vue": "^3.2.0", "@vue/compiler-sfc": "^3.2.31", "eslint": "^8.11.0", "prettier": "^3.5.3", "sass": "^1.49.9", "ssh2-sftp-client": "^10.0.3", "typescript": "^4.6.2", "unocss": "^66.1.3", "unplugin-auto-import": "^0.6.4", "unplugin-vue-components": "^0.18.0", "vite": "^3.0.0", "vite-plugin-mock": "2.9.6", "vue-tsc": "^0.32.1"}}