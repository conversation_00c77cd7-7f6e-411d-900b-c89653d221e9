<template>
  <div class="layout-container">
    <div class="layout-container-form flex space-between">
      <div class="form-left">
        <el-button
          type="primary"
          :icon="Plus"
          @click="handleAdd"
          class="add-btn"
          size="default"
        >
          <span>{{ $t('message.common.add') }}</span>
        </el-button>
      </div>
      <div class="layout-container-form-search">
        <el-input
          v-model="query.name"
          placeholder="请输入分类名称搜索..."
          class="search-input"
          clearable
          size="default"
        >
          <template #prefix>
            <el-icon class="search-icon">
              <Search />
            </el-icon>
          </template>
        </el-input>
        <el-button
          type="primary"
          :icon="Search"
          class="search-btn"
          @click="getTableData(true)"
          size="default"
        >
          {{ $t('message.common.search') }}
        </el-button>
      </div>
    </div>
    <div class="layout-container-table">
      <!-- 使用 he-tree-vue 实现拖拽树形结构 -->
      <div class="category-tree-container" v-loading="loading || isDragging">
        <div class="tree-table-wrapper">
          <div class="tree-header">
            <div class="header-cell name-cell">
              <span>分类名称</span>
            </div>
            <div class="header-cell icon-cell">
              <span>图标</span>
            </div>
            <div class="header-cell sort-cell">
              <span>排序</span>
            </div>
            <div class="header-cell status-cell">
              <span>状态</span>
            </div>
            <div class="header-cell action-cell">
              <span>操作</span>
            </div>
          </div>
          <TreeComponent
            :value="tableData"
            class="he-tree-custom"
            :indent="50"
            :unfoldWhenDragover="true"
            :unfoldWhenDragoverDelay="500"
            :edgeScroll="true"
            :edgeScrollTriggerMargin="80"
            :edgeScrollSpeed="0.5"
            :edgeScrollTriggerMode="'mouse'"
            :edgeScrollSpecifiedContainerY="getScrollContainer"
            @input="onTreeInput"
            @change="onTreeChange"
            @drop="onTreeDrop"
            @before-drop="onBeforeDrop"
          >
            <template #default="{ node, path, tree }">
              <div class="tree-row">
                <!-- 展开/收起按钮 -->
                <div
                  class="expand-btn"
                  @click="tree.toggleFold(node, path)"
                  v-if="hasChildren(node)"
                >
                  <el-icon :class="{ 'rotate-90': !node.$folded }">
                    <ArrowRight />
                  </el-icon>
                </div>
                <div class="expand-btn placeholder" v-else></div>

                <!-- 拖拽手柄 -->
                <div class="drag-handle tree-drag-handle">
                  <el-icon>
                    <Rank />
                  </el-icon>
                </div>

                <!-- 名称列 -->
                <div class="cell name-cell">
                  <span class="category-name">{{ node.name }}</span>
                </div>

                <!-- 图标列 -->
                <div class="cell icon-cell">
                  <div class="icon-wrapper">
                    <imagePreview
                      :imgurl="node.extra"
                      :width="80"
                      :height="80"
                      :type="1"
                      v-if="node.extra"
                    />
                    <div class="no-icon" v-else>
                      <el-icon>
                        <Picture />
                      </el-icon>
                    </div>
                  </div>
                </div>

                <!-- 排序列 -->
                <div class="cell sort-cell">
                  <span class="sort-number">{{ node.sort }}</span>
                </div>

                <!-- 状态列 -->
                <div class="cell status-cell">
                  <div class="status-wrapper">
                    <el-tag
                      :type="node.status ? 'success' : 'danger'"
                      size="small"
                      class="status-tag"
                    >
                      {{ node.status ? '启用' : '禁用' }}
                    </el-tag>
                    <el-switch
                      v-model="node.status"
                      size="small"
                      :active-value="true"
                      :inactive-value="false"
                      @change="handleUpdateStatus(node)"
                      class="status-switch"
                    />
                  </div>
                </div>

                <!-- 操作列 -->
                <div class="cell action-cell">
                  <div class="action-buttons">
                    <el-button
                      type="info"
                      size="small"
                      @click="handleEdit(node)"
                    >
                      编辑
                    </el-button>
                    <el-popconfirm
                      title="确定要删除这个分类吗？"
                      @confirm="handleDel([node])"
                    >
                      <template #reference>
                        <el-button type="danger" size="small">删除</el-button>
                      </template>
                    </el-popconfirm>
                  </div>
                </div>
              </div>
            </template>
          </TreeComponent>
        </div>
      </div>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, nextTick } from 'vue'
  import { Page } from '@/components/table/type'
  import { LayerInterface } from '@/components/layer/index.vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import Layer from './layer.vue'
  import {
    Plus,
    Search,
    ArrowRight,
    Rank,
    Picture,
  } from '@element-plus/icons-vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import { Tree, Fold, Draggable } from 'he-tree-vue'
  import 'he-tree-vue/dist/he-tree-vue.css'
  import {
    treeCategroy,
    deleteCategroy,
    categroyUpdateStatus,
    updateCategroy,
  } from '@/api/categoryApi'
  import { TreeCategroyRes } from '@/type/category.type'

  // 扩展类型以支持 he-tree-vue 的 children 属性
  interface HeTreeNode extends TreeCategroyRes {
    children?: HeTreeNode[]
    $folded?: boolean
  }

  // 创建混合了折叠和拖拽功能的树组件
  const TreeComponent = (Tree as any).mixPlugins([Fold, Draggable])

  // 存储搜索用的数据
  const query = reactive({
    name: '',
    type: 1,
    status: -1,
    // dateLimit:''
  })

  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
  })

  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })

  const loading = ref(true)
  const tableData = ref<HeTreeNode[]>([])
  const isDragging = ref(false)

  // 判断节点是否有子节点
  const hasChildren = (node: HeTreeNode) => {
    return node.children && node.children.length > 0
  }

  // 获取滚动容器 - 返回更大的容器以支持超出边界的拖拽滚动
  const getScrollContainer = () => {
    // 返回 layout-container-table 容器，这样即使拖拽超出树形容器也能触发滚动
    return (
      document.querySelector('.layout-container-table') ||
      document.querySelector('.category-tree-container')
    )
  }

  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    treeCategroy(params)
      .then(res => {
        let data: any = res
        data.forEach((d: any) => {
          d.loading = false
        })
        // 转换数据结构以适配 he-tree-vue
        tableData.value = convertToHeTreeData(data)
      })
      .catch(() => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 转换数据结构以适配 he-tree-vue
  const convertToHeTreeData = (data: TreeCategroyRes[]): HeTreeNode[] => {
    return data.map(item => {
      const converted: HeTreeNode = { ...item }
      if (item.child && item.child.length > 0) {
        converted.children = convertToHeTreeData(item.child)
        // 确保有子节点的节点默认是折叠状态，这样拖拽时才能自动展开
        converted.$folded = true
        delete (converted as any).child
      }
      return converted
    })
  }

  // he-tree-vue 事件处理
  const onTreeInput = (newData: HeTreeNode[]) => {
    tableData.value = newData
  }

  const onTreeChange = (newData: HeTreeNode[]) => {
    console.log('Tree structure changed:', newData)
  }

  const onTreeDrop = (store: any) => {
    console.log('Drop event:', store)
    handleTreeDrop(store)
  }

  const onBeforeDrop = (store: any) => {
    console.log('Before drop event:', store)
    isDragging.value = true
    // 可以在这里添加拖拽前的验证逻辑
    return true // 返回 true 允许拖拽，false 阻止拖拽
  }

  // 处理拖拽完成事件
  const handleTreeDrop = async (store: any) => {
    try {
      const { dragNode, targetParent, startPath, targetPath } = store

      // 根据 targetPath 计算真正的目标父级
      let realTargetParent = null
      let newParentId = 0

      if (targetPath && targetPath.length > 1) {
        // 如果 targetPath 长度大于1，说明拖拽到了某个节点的子级
        // 需要根据 targetPath 找到真正的父级节点
        const parentPath = targetPath.slice(0, -1) // 去掉最后一个索引，得到父级路径
        realTargetParent = getNodeByPath(parentPath)
        if (realTargetParent && realTargetParent.id) {
          newParentId = realTargetParent.id
        }
      } else if (targetPath && targetPath.length === 1) {
        // 如果 targetPath 长度为1，说明拖拽到根级
        newParentId = 0
      }

      console.log('拖拽信息:', {
        dragNode: dragNode,
        targetParent: targetParent,
        realTargetParent: realTargetParent,
        startPath: startPath,
        targetPath: targetPath,
        dragNodeId: dragNode.id,
        dragNodeName: dragNode.name,
        originalParentId: dragNode.pid,
        newParentId: newParentId,
      })

      // 检查是否真的改变了父级
      const originalParentId = dragNode.pid || 0
      if (originalParentId === newParentId) {
        console.log('父级没有改变，跳过更新')
        return
      }

      // 先更新本地数据，提供即时反馈
      updateLocalData(dragNode.id, newParentId)

      // 然后更新数据库
      try {
        await updateItemParent(dragNode.id, newParentId)
        ElMessage({
          type: 'success',
          message: '分类层级更新成功',
        })
      } catch (error) {
        // 如果数据库更新失败，恢复本地数据
        console.error('数据库更新失败，恢复本地数据:', error)
        getTableData(false)
        throw error
      } finally {
        isDragging.value = false
      }
    } catch (error) {
      console.error('分类层级更新失败:', error)
      ElMessage({
        type: 'error',
        message: '分类层级更新失败',
      })
      // 重新获取数据以恢复原始状态
      getTableData(false)
      isDragging.value = false
    }
  }

  // 更新本地数据，提供即时反馈
  const updateLocalData = (nodeId: number, newParentId: number) => {
    // 由于 he-tree-vue 已经更新了本地数据结构，我们只需要更新节点的 pid 属性
    const updateNodePid = (nodes: HeTreeNode[]): boolean => {
      for (const node of nodes) {
        if (node.id === nodeId) {
          node.pid = newParentId
          return true
        }
        if (node.children && updateNodePid(node.children)) {
          return true
        }
      }
      return false
    }

    updateNodePid(tableData.value)
  }

  // 根据路径获取节点
  const getNodeByPath = (path: number[]): HeTreeNode | null => {
    let current: HeTreeNode[] = tableData.value
    let node: HeTreeNode | null = null

    for (let i = 0; i < path.length; i++) {
      const index = path[i]
      if (current && current[index]) {
        node = current[index]
        current = node.children || []
      } else {
        return null
      }
    }

    return node
  }

  // 更新单个项目的父级关系
  const updateItemParent = async (id: number, newParentId: number) => {
    // 找到要更新的项目
    const findItem = (
      items: HeTreeNode[],
      targetId: number
    ): HeTreeNode | null => {
      for (const item of items) {
        if (item.id === targetId) {
          return item
        }
        if (item.children) {
          const found = findItem(item.children, targetId)
          if (found) return found
        }
      }
      return null
    }

    const item = findItem(tableData.value, id)
    if (!item) {
      throw new Error('找不到要更新的分类项目')
    }

    // 调用更新API，只更新父级ID，保持其他属性不变
    await updateCategroy({
      id: item.id!,
      name: item.name!,
      pid: newParentId, // 这是关键：更新父级ID
      sort: item.sort!, // 保持原有排序
      status: item.status!,
      type: item.type!,
      extra: item.extra || '',
      url: item.url || '',
    })
  }

  // 删除功能
  const handleDel = (data: object[]) => {
    let params = {
      id: data
        .map((e: any) => {
          return e.id
        })
        .join(','),
    }
    deleteCategroy(params).then(() => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  // 新增弹窗功能
  const handleAdd = () => {
    layer.title = '新增分类'
    layer.show = true
    delete layer.row
  }
  // 编辑弹窗功能
  const handleEdit = (row: any) => {
    layer.title = '编辑分类'
    layer.row = row
    layer.show = true
  }
  // 状态编辑功能
  const handleUpdateStatus = (row: any) => {
    if (!row.id) {
      return
    }
    row.loading = true
    let params = {
      id: row.id,
      status: row.status,
    }
    categroyUpdateStatus(params.id)
      .then(() => {
        ElMessage({
          type: 'success',
          message: '状态变更成功',
        })
      })
      .catch(() => {
        row.status = !row.status
        ElMessage({
          type: 'error',
          message: '状态变更失败',
        })
      })
      .finally(() => {
        row.loading = false
      })
  }

  // 组件挂载后初始化
  onMounted(() => {
    getTableData(true)
  })
</script>

<style lang="scss" scoped>
  // 顶部操作区域样式
  .layout-container-form {
    background: var(--system-page-background);
    border-radius: 8px;
    padding: 20px 24px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    border: 1px solid var(--system-page-border-color);

    .form-left {
      .add-btn {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 600;
        letter-spacing: 0.5px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
        }

        &:active {
          transform: translateY(0);
        }

        span {
          margin-left: 4px;
        }
      }
    }

    .layout-container-form-search {
      display: flex;
      align-items: center;
      gap: 12px;

      .search-input {
        width: 320px;

        :deep(.el-input__wrapper) {
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
          border: 1px solid var(--system-page-border-color);
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--el-color-primary-light-5);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.1);
          }

          &.is-focus {
            border-color: var(--el-color-primary);
            box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
          }
        }

        :deep(.el-input__inner) {
          height: 40px;
          font-size: 14px;
          color: var(--system-page-color);

          &::placeholder {
            color: var(--system-page-tip-color);
            font-size: 13px;
          }
        }

        .search-icon {
          color: var(--system-page-tip-color);
          transition: color 0.3s ease;
        }

        &:hover .search-icon {
          color: var(--el-color-primary);
        }
      }

      .search-btn {
        height: 40px;
        padding: 0 20px;
        border-radius: 8px;
        font-weight: 600;
        letter-spacing: 0.5px;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(64, 158, 255, 0.3);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }

  .example-showcase .el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
  }

  .el-button--text {
    margin-right: 15px;
  }

  .dialog-footer button:first-child {
    margin-right: 10px;
  }

  // 自定义树形结构样式
  .category-tree-container {
    background: var(--system-page-background);
    border-radius: 8px;
    overflow: hidden;
    border: 1px solid var(--system-page-border-color);
    box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.08);
    transition: box-shadow 0.3s ease;

    &:hover {
      box-shadow: 0 6px 30px 0 rgba(0, 0, 0, 0.12);
    }
  }

  .tree-table-wrapper {
    width: 100%;
    overflow: hidden;
    border-radius: 8px;
  }

  .tree-header {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
    border-bottom: 1px solid var(--system-page-border-color);
    padding: 0 20px;
    height: 56px;
    font-weight: 600;
    color: var(--system-page-color);
    font-size: 14px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent 0%,
        var(--system-page-border-color) 20%,
        var(--system-page-border-color) 80%,
        transparent 100%
      );
    }

    span {
      color: var(--system-page-color);
      font-weight: 600;
      letter-spacing: 0.5px;
    }
  }

  .header-cell {
    display: flex;
    align-items: center;
    position: relative;

    &.name-cell {
      flex: 1;
      min-width: 220px;
      padding-left: 60px; // 为展开按钮和拖拽手柄留空间
    }

    &.icon-cell {
      width: 120px;
      justify-content: center;
    }

    &.sort-cell {
      width: 100px;
      justify-content: center;
    }

    &.status-cell {
      width: 160px;
      justify-content: center;
    }

    &.action-cell {
      width: 150px;
      justify-content: center;
    }
  }

  :deep(.tree-placeholder-node) {
    // 占位符样式
    background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%) !important;
    border: 2px dashed var(--el-color-primary) !important;
    border-radius: 8px;
    min-height: 90px; // 与树行高度保持一致
    margin: 4px 0;
    opacity: 0.9;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &::before {
      content: '拖拽到此处';
      color: var(--el-color-primary);
      font-size: 12px;
      font-weight: 500;
    }
  }

  // he-tree-vue 自定义样式
  .he-tree-custom {
    .tree-node {
      border: none !important;
      padding: 0 !important;
      margin: 0 !important;
    }

    .tree-node-back {
      padding: 0 !important;
      background: transparent !important;
    }

    .tree-children {
      margin-left: 0 !important;
    }

    // 拖拽时的样式
    .tree-node.dragging {
      opacity: 0.9;
      background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%) !important;
      border: 2px solid var(--el-color-primary) !important;
      border-radius: 8px;
      box-shadow: 0 8px 25px rgba(64, 158, 255, 0.3);
      transform: rotate(2deg) scale(1.02);
      z-index: 1000;
      transition: all 0.2s ease;
    }

    // 拖拽目标高亮
    .tree-node-back.droppable {
      background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%) !important;
      border: 2px solid var(--el-color-primary) !important;
      border-radius: 8px;
      box-shadow: 0 4px 15px rgba(64, 158, 255, 0.2);
    }
  }

  // 树行样式
  .tree-row {
    display: flex;
    align-items: center;
    min-height: 90px;
    background: var(--system-page-background);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    width: 100%;
    box-sizing: border-box;
    border-bottom: 1px solid var(--system-page-border-color);
    padding: 0 20px;

    &:hover {
      background: linear-gradient(135deg, #fafbfc 0%, #f0f2f5 100%);
      transform: translateX(2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    }

    &:last-child {
      border-bottom: none;
    }

    // 添加左侧装饰线
    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      bottom: 0;
      width: 3px;
      background: transparent;
      transition: background 0.3s ease;
    }

    &:hover::before {
      background: linear-gradient(
        180deg,
        var(--el-color-primary) 0%,
        var(--el-color-primary-light-3) 100%
      );
    }
  }

  .expand-btn {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-right: 10px;
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(64, 158, 255, 0.05);
    border: 1px solid transparent;

    &:hover {
      background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
      border-color: var(--el-color-primary-light-5);
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }

    &.placeholder {
      cursor: default;
      background: transparent;
      border: none;

      &:hover {
        background: transparent;
        transform: none;
        box-shadow: none;
      }
    }

    .el-icon {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      color: var(--el-color-primary);
      font-size: 16px;
      font-weight: 600;

      &.rotate-90 {
        transform: rotate(90deg);
        color: var(--el-color-primary-dark-2);
      }
    }
  }

  .drag-handle {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: move;
    margin-right: 14px;
    color: var(--system-page-tip-color);
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: rgba(96, 98, 102, 0.05);
    border: 1px solid transparent;

    &:hover {
      color: var(--el-color-primary);
      background: linear-gradient(135deg, #ecf5ff 0%, #d9ecff 100%);
      border-color: var(--el-color-primary-light-7);
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
    }

    .el-icon {
      font-size: 16px;
      font-weight: 600;
    }
  }

  .cell {
    display: flex;
    align-items: center;

    &.name-cell {
      flex: 1;
      min-width: 220px;

      .category-name {
        font-weight: 600;
        color: var(--system-page-color);
        font-size: 15px;
        letter-spacing: 0.3px;
        transition: color 0.3s ease;

        .tree-row:hover & {
          color: var(--el-color-primary);
        }
      }
    }

    &.icon-cell {
      width: 120px;
      justify-content: center;

      .icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .no-icon {
          width: 80px;
          height: 80px;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
          border: 2px dashed var(--system-page-border-color);
          border-radius: 8px;
          color: var(--system-page-tip-color);
          transition: all 0.3s ease;

          &:hover {
            border-color: var(--el-color-primary-light-5);
            background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
            color: var(--el-color-primary);
          }

          .el-icon {
            font-size: 28px;
            transition: transform 0.3s ease;
          }

          &:hover .el-icon {
            transform: scale(1.1);
          }
        }
      }
    }

    &.sort-cell {
      width: 100px;
      justify-content: center;

      .sort-number {
        font-weight: 600;
        color: var(--system-page-color);
        font-size: 14px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        padding: 4px 12px;
        border-radius: 12px;
        border: 1px solid var(--system-page-border-color);
        transition: all 0.3s ease;

        .tree-row:hover & {
          background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
          border-color: var(--el-color-primary-light-5);
          color: var(--el-color-primary);
        }
      }
    }

    &.status-cell {
      width: 160px;
      justify-content: center;

      .status-wrapper {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        border: 1px solid var(--system-page-border-color);

        .status-tag {
          min-width: 48px;
          text-align: center;
          font-weight: 500;
          border-radius: 6px;
        }

        .status-switch {
          margin-left: 4px;
        }
      }
    }

    &.action-cell {
      width: 150px;
      justify-content: center;

      .action-buttons {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 4px 8px;
        border-radius: 8px;

        .el-button {
          font-weight: 500;
          border-radius: 6px;

          &.el-button--info {
            // info 类型按钮已经有默认边框
          }

          &.el-button--danger {
            // danger 类型按钮已经有默认边框
          }
        }
      }
    }
  }
</style>
