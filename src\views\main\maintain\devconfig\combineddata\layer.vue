<template>
  <Layer :layer="layer" @confirm="submit(ruleForm)" ref="layerDom">
    <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" style="margin-right:30px;">
      <el-form-item label="数据组名称" prop="name">
        <el-input v-model="form.name"></el-input>
      </el-form-item>
      <el-form-item label="数据简介" prop="info">
        <el-input v-model="form.info"></el-input>
      </el-form-item>
      <el-form-item label="表单数据" prop="formId">
        {{ form.formId }}
        <el-button type="primary" @click="formListShow = true">选择模板数据</el-button>

      </el-form-item>

    </el-form>
    <getFormId :dialogTableVisible="true" @setFormId="setFormId" v-show="formListShow"></getFormId>

  </Layer>
</template>

<script lang="ts" setup>
import getFormId from '../../components/getFormId.vue'
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import type { ElFormItemContext } from 'element-plus/lib/el-form/src/token'
import { defineEmits, defineProps, ref, reactive } from 'vue'
import Layer from '@/components/layer/index.vue'
import { ElMessage } from 'element-plus'
import { saveGroup, updateGroup } from '@/api/maintain'
import type { GroupParams } from '@/type/maintain.type'
import type { FormInstance, FormRules } from 'element-plus'


const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})

const emit = defineEmits(['getTableData'])
const vfdRef = ref(null)
const ruleForm: Ref<ElFormItemContext | null> = ref(null)
const layerDom: Ref<LayerType | null> = ref(null)
let form = ref({
  id: 0,
  name: '',
  info: '',
  formId: 0
})

const rules = reactive<FormRules>({
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
  info: [{ required: true, message: '请输入简介', trigger: 'blur' }],
  formId: [{ required: true, message: '请选择模板', trigger: 'blur' }],
})
init()
function init() { // 用于判断新增还是编辑功能

  if (props.layer.row) {
    form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
  } else {

  }
}
const dialogTableVisible = ref(false)
const setFormId = (val: number) => {
  form.value.formId = val
  formListShow.value = false

}
const formListShow = ref(false)

const submit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      let params = form.value
      if (props.layer.row) {
        updateForm(params)
      } else {
        addForm(params)
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}


// 新增提交事件
function addForm(params: GroupParams) {
  saveGroup(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '新增成功'
      })
      emit('getTableData', true)
      layerDom.value && layerDom.value.close()
    })
}
// 编辑提交事件
function updateForm(params: GroupParams) {
  updateGroup(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '编辑成功'
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
}




</script>

<style lang="scss" scoped></style>