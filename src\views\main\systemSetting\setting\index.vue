<template>
  <div class="layout-container">
    <div style="padding: 15px;">

      <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane :label="item.name" :name="item.id" v-for="item in categoryList" :key="item.id">
          <div v-if="item.child.length > 0">
            <el-tabs v-model="activeName2" class="demo-tabs" @tab-click="handleClick2" type="border-card">
              <el-tab-pane :label="item2.name" :name="item2.extra" v-for="item2, index in item.child" :key="index">
                <Parser :form-conf="formConf.content" @submit="handlerSubmit"  v-if="formConf.render"
                  :is-edit="formConf.isEdit" :form-edit-data="currentEditData" />
              </el-tab-pane>
            </el-tabs>
          </div>
          <div v-else>
            <el-empty description="无数据" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue"
import { treeCategroy } from '@/api/categoryApi'
import { getFormConfigInfo, configInfo, saveFormConfig } from '@/api/systemFormConfig'
import Parser from '@/components/FormGenerator/components/parser/Parser.vue'
import { ElMessage } from 'element-plus'
import type { ConfigInfoRes, Fields, SaveFormConfigParams } from '@/type/systemFormConfig.type'
import type { TabsPaneContext } from 'element-plus'
//tab栏目切换
const activeName = ref<string>('')
const activeName2 = ref<string>('')
const loading = ref(false)
const handleClick = (tab: { props: { name: any } }) => {
  categoryList.value.forEach((item: { id: any; child: { extra: string }[] }) => {
    if (item.id == tab.props.name) {
      activeName2.value = item.child[0].extra
      getForm(item.child[0].extra)
    }
  });
}
const handleClick2 = (tab: TabsPaneContext) => {
  getForm(tab.props.name)
}
//导航栏list
const categoryList = ref()
const getlist = () => {
  treeCategroy({ type: 6, status: 1 }).then((res) => {
    categoryList.value = res
    activeName.value = categoryList.value[0].id
    if (categoryList.value[0].child.length > 0) {
      activeName2.value = categoryList.value[0].child[0].extra
      getForm(categoryList.value[0].child[0].extra)
    } else {
      getForm(categoryList.value[0].extra)
    }
  })
}
const formConf = ref({
  content: { fields: [] }, id: null, render: false, isEdit: false
})
const currentEditData = ref<ConfigInfoRes>({})
const handlerSubmit = (data: any, id: any) => {
  let fields: Fields[] = []
  for (const key in data) {
    let obj = {
      name: '',
      title: '',
      value: ''
    }
    obj.name = key,
      obj.title = key
    if (data[key].constructor === String) {
      obj.value = data[key]
    } else {
      obj.value = data[key].toString()
    }
    fields.push(obj)
  }
  let obj: SaveFormConfigParams = {
    fields: fields,
    sort: 0,
    status: true,
    id: currentEditData.value.id
  }
  saveFormConfig(obj).then(res => {
    ElMessage({
      message: '操作成功',
      type: 'success',
    })
  })
}
const getForm = (id: number | string) => {
  loading.value = true
  formConf.value = { content: { fields: [] }, id: null, render: false, isEdit: false }
  if (!id) {
    ElMessage({
      type: 'warning',
      message: '请前往配置分类关联表单'
    })
    loading.value = false
    return

  }
  getFormConfigInfo({ id: id }).then(res => {
    if (res) {
      formConf.value.content = JSON.parse(res.content)
    }
 

    formConf.value.isEdit = true
    getFormInfo(id)
  }).finally(() => {
    loading.value = false
  })
}
const getFormInfo = (id: number | string) => {
  configInfo({ formId: id }).then(res => {
    let data = res
    currentEditData.value = data
    formConf.value.render=true
  })
}
getlist()

</script>

<style lang="scss" scoped></style>
