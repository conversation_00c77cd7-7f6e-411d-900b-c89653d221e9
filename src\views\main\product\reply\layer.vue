<template>
  <Layer :layer="layer" @confirm="submit" ref="layerDom">
    <el-descriptions class="margin-top" title="订单信息" :column="2">
      <el-descriptions-item label="订单id">{{ form.id }}</el-descriptions-item>
      <el-descriptions-item label="订单号">{{ form.orderNum }}</el-descriptions-item>
      <el-descriptions-item label="实际支付">{{ form.payPrice }}元</el-descriptions-item>
      <el-descriptions-item label="支付方式">{{ form.payTypeStr || '暂无' }}</el-descriptions-item>
      <el-descriptions-item label="收货地址">{{ form.userAddress }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ form.createTime || '暂无' }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions class="margin-top" title="商品信息" :column="1">
      <el-descriptions-item>
        <el-table :data="form.orderInfo" :border="false">
          <el-table-column label="商品图片" prop="name" align="center">
            <template #default="scope">
              <imagePreview :imgurl="scope.row.info.image"></imagePreview>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" align="center">
            <template #default="scope">
              {{ scope.row.info.productName }}
            </template>
          </el-table-column>
          <el-table-column label="规格" align="center">
            <template #default="scope">
              {{ scope.row.info.sku }}
            </template>
          </el-table-column>
          <el-table-column label="价格" align="center">
            <template #default="scope">
              {{ form.payChannelStr === '金币支付' ? scope.row.info.goldPrice + '金币' : scope.row.info.price + '元' }}
            </template>
          </el-table-column>
          <el-table-column label="商品分数" align="center">
            <template #default="scope">
              <el-rate v-model="scope.row.info.productScore" disabled />
            </template>
          </el-table-column>
          <el-table-column label="服务分数" align="center" >
            <template #default="scope">
              <el-rate v-model="scope.row.info.serviceScore" disabled  />
            </template>
          </el-table-column>
        </el-table>
      </el-descriptions-item>
    </el-descriptions>
  </Layer>
</template>
  
<script lang="ts" setup>
import type { LayerType } from '@/components/layer/index.vue'
import { ref } from 'vue'
import type { Ref } from 'vue'
import { orderInfo } from '@/api/product'
import Layer from '@/components/layer/index.vue'
import imagePreview from '@/components/imagePreview/index.vue'

const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})
const layerDom: Ref<LayerType | null> = ref(null)
let form = ref<any>({
  orderId: ''
})
const getOrderInfo = () => {
  orderInfo({ orderId: form.value.orderId }).then(res => {
    form.value = res
    //   添加商品分数、服务分数
    form.value.orderInfo.forEach((d: any) => {
      d.info.productScore = props.layer.row.productScore
      d.info.serviceScore = props.layer.row.serviceScore
    })
  })
}
init()
function init() { // 用于判断新增还是编辑功能
  console.log(props.layer.row, 'props.layer.row')
  if (props.layer.row) {
    form.value.orderId = JSON.parse(JSON.stringify(props.layer.row.oid)) // 数量量少的直接使用这个转
    getOrderInfo()
  } else {
  }
}
function submit() {
  layerDom.value && layerDom.value.close()
}

</script>
  
<style lang="scss" scoped></style>