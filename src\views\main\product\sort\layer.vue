<template>
  <Layer :layer="layer" @confirm="submit(ruleForm)" ref="layerDom">
    <el-form
      :model="form"
      :rules="rules"
      ref="ruleForm"
      label-width="120px"
      style="margin-right: 30px"
    >
      <el-form-item label="分类名称" prop="name">
        <el-input v-model="form.name" placeholder="请输入名称"></el-input>
      </el-form-item>

      <el-form-item label="父级" prop="pid">
        <div class="category-selector-wrapper">
          <el-input
            v-model="selectedCategoryName"
            placeholder="选择父级分类"
            readonly
            style="width: 100%; cursor: pointer"
            @click="categoryPopoverVisible = !categoryPopoverVisible"
          >
            <template #suffix>
              <div
                class="flex items-center justify-center"
                :class="{ 'rotate-180': categoryPopoverVisible }"
              >
                <ArrowDown class="w-[15px] h-[15px]" />
              </div>
            </template>
          </el-input>

          <div v-show="categoryPopoverVisible" class="category-dropdown">
            <div class="category-selector">
              <el-input
                v-model="searchText"
                placeholder="搜索分类..."
                clearable
                style="margin-bottom: 8px"
              >
                <template #prefix>
                  <div class="flex items-center justify-center">
                    <Search class="w-[15px] h-[15px]" />
                  </div>
                </template>
              </el-input>

              <div
                class="category-tree"
                style="max-height: 300px; overflow-y: auto"
              >
                <div class="category-item" @click="selectCategory(0, '主菜单')">
                  <div class="category-content">
                    <span
                      class="category-name"
                      :class="{ selected: form.pid === 0 }"
                    >
                      主菜单
                    </span>
                  </div>
                </div>

                <CategoryTreeNode
                  v-for="category in filteredCategories"
                  :key="category.id"
                  :category="category"
                  :level="0"
                  :selected-id="form.pid"
                  :search-text="searchText"
                  @select="data => selectCategory(data.id, data.name)"
                />
              </div>
            </div>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="分类图标" prop="extra">
        <div>
          <uploadIcon v-model="form.extra" />
        </div>
      </el-form-item>
      <el-form-item label="排序" prop="sort">
        <el-input-number
          v-model="form.sort"
          :min="0"
          :step="1"
        ></el-input-number>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-switch
          v-model="form.status"
          :active-value="true"
          :inactive-value="false"
        />
      </el-form-item>
    </el-form>
  </Layer>
</template>

<script lang="ts" setup>
  import type { LayerType } from '@/components/layer/index.vue'
  import type { Ref } from 'vue'
  import type { FormInstance, FormRules } from 'element-plus'
  import {
    ref,
    defineProps,
    defineEmits,
    reactive,
    computed,
    onMounted,
    onUnmounted,
  } from 'vue'
  import Layer from '@/components/layer/index.vue'
  import { treeCategroy, updateCategroy, addCategroy } from '@/api/categoryApi'
  import { ElMessage } from 'element-plus'
  import { ArrowDown, Search, ArrowRight } from '@element-plus/icons-vue'
  import uploadIcon from '@/components/uploadIcon/index.vue'
  import CategoryTreeNode from './CategoryTreeNode.vue'
  import type {
    TreeCategroyRes,
    updateCategroyParms,
  } from '@/type/category.type'

  // 定义扁平化选项的接口
  interface FlattenedOption {
    id: number
    name: string
    displayName: string
    level: number
  }
  const props = defineProps({
    layer: {
      type: Object,
      default: () => {
        return {
          show: false,
          title: '',
          showButton: true,
        }
      },
    },
  })
  const emit = defineEmits(['getTableData'])
  const ruleForm = ref<FormInstance>()
  const layerDom: Ref<LayerType | null> = ref(null)
  const form = ref({
    id: 0,
    status: false,
    sort: 1,
    name: '',
    extra: '',
    type: 1,
    url: '',
    pid: 0,
  })
  //获取输入框的值
  const options = ref<TreeCategroyRes[]>([])
  treeCategroy({ type: 1, status: -1 }).then(res => {
    options.value = res
    // 数据加载完成后，重新设置选中的分类名称
    updateSelectedCategoryName()
  })

  // 递归函数：将树形数据转换为扁平化的选项列表
  const flattenTreeData = (
    data: TreeCategroyRes[],
    level: number = 0,
    parentPrefix: string = ''
  ): FlattenedOption[] => {
    const result: FlattenedOption[] = []

    data.forEach((item, index) => {
      if (item.id && item.name) {
        const isLastItem = index === data.length - 1
        let displayName = ''

        if (level === 0) {
          // 顶级分类不添加前缀
          displayName = item.name
        } else {
          // 子级分类添加树形结构前缀
          const currentPrefix = isLastItem ? '└─ ' : '├─ '
          displayName = `${parentPrefix}${currentPrefix}${item.name}`
        }

        result.push({
          id: item.id,
          name: item.name,
          displayName: displayName,
          level: level,
        })

        // 递归处理子节点
        if (item.child && item.child.length > 0) {
          const nextParentPrefix =
            level === 0 ? '　' : parentPrefix + (isLastItem ? '　　' : '│　')
          result.push(
            ...flattenTreeData(item.child, level + 1, nextParentPrefix)
          )
        }
      }
    })

    return result
  }

  // 新的状态变量
  const categoryPopoverVisible = ref(false)
  const searchText = ref('')
  const selectedCategoryName = ref('主菜单')

  // 计算属性：过滤后的分类列表
  const filteredCategories = computed(() => {
    let categories = options.value

    // 如果是编辑模式，排除当前分类及其所有子分类
    if (props.layer.row && form.value.id) {
      categories = excludeCurrentAndChildren(categories, form.value.id)
    }

    if (!searchText.value) {
      return categories
    }
    return filterCategories(categories, searchText.value.toLowerCase())
  })

  // 排除当前分类及其所有子分类
  const excludeCurrentAndChildren = (
    categories: TreeCategroyRes[],
    excludeId: number
  ): TreeCategroyRes[] => {
    const result: TreeCategroyRes[] = []

    categories.forEach(category => {
      // 如果当前分类不是要排除的分类
      if (category.id !== excludeId) {
        // 检查是否是要排除分类的子分类
        if (!isChildOfCategory(category, excludeId, options.value)) {
          const filteredChildren = category.child
            ? excludeCurrentAndChildren(category.child, excludeId)
            : []

          result.push({
            ...category,
            child: filteredChildren,
          })
        }
      }
    })

    return result
  }

  // 检查分类是否是指定分类的子分类
  const isChildOfCategory = (
    category: TreeCategroyRes,
    parentId: number,
    allCategories: TreeCategroyRes[]
  ): boolean => {
    // 递归查找父分类
    const findParent = (
      categories: TreeCategroyRes[],
      targetId: number
    ): TreeCategroyRes | null => {
      for (const cat of categories) {
        if (cat.id === targetId) {
          return cat
        }
        if (cat.child) {
          const found = findParent(cat.child, targetId)
          if (found) return found
        }
      }
      return null
    }

    // 从当前分类开始，向上查找是否有指定的父分类
    const checkPath = (currentCategory: TreeCategroyRes): boolean => {
      if (!currentCategory.pid || currentCategory.pid === 0) {
        return false
      }
      if (currentCategory.pid === parentId) {
        return true
      }
      const parent = findParent(allCategories, currentCategory.pid)
      return parent ? checkPath(parent) : false
    }

    return checkPath(category)
  }

  // 递归过滤分类
  const filterCategories = (
    categories: TreeCategroyRes[],
    searchTerm: string
  ): TreeCategroyRes[] => {
    const result: TreeCategroyRes[] = []

    categories.forEach(category => {
      const matchesSearch = category.name?.toLowerCase().includes(searchTerm)
      const filteredChildren = category.child
        ? filterCategories(category.child, searchTerm)
        : []

      if (matchesSearch || filteredChildren.length > 0) {
        result.push({
          ...category,
          child:
            filteredChildren.length > 0 ? filteredChildren : category.child,
        })
      }
    })

    return result
  }

  // 选择分类的函数
  const selectCategory = (id: number, name: string) => {
    form.value.pid = id
    selectedCategoryName.value = name
    categoryPopoverVisible.value = false
  }

  // 点击外部关闭下拉菜单
  const handleClickOutside = (event: Event) => {
    const target = event.target as HTMLElement
    const wrapper = document.querySelector('.category-selector-wrapper')
    if (wrapper && !wrapper.contains(target)) {
      categoryPopoverVisible.value = false
    }
  }

  onMounted(() => {
    document.addEventListener('click', handleClickOutside)
  })

  onUnmounted(() => {
    document.removeEventListener('click', handleClickOutside)
  })

  // 根据ID获取分类名称
  const getCategoryNameById = (
    categories: TreeCategroyRes[],
    id: number
  ): string => {
    for (const category of categories) {
      if (category.id === id) {
        return category.name || ''
      }
      if (category.child) {
        const found = getCategoryNameById(category.child, id)
        if (found) return found
      }
    }
    return ''
  }

  // 更新选中的分类名称
  const updateSelectedCategoryName = () => {
    if (form.value.pid === 0) {
      selectedCategoryName.value = '主菜单'
    } else {
      const categoryName = getCategoryNameById(options.value, form.value.pid)
      selectedCategoryName.value = categoryName || '主菜单'
    }
  }
  const rules = reactive<FormRules>({
    ruleName: [{ required: true, message: '请输入规则名称', trigger: 'blur' }],
  })
  init()
  function init() {
    // 用于判断新增还是编辑功能
    if (props.layer.row) {
      form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
      // 确保 extra 字段不为 null
      if (form.value.extra === null || form.value.extra === undefined) {
        form.value.extra = ''
      }
    }
    // 设置选中的分类名称（会在数据加载完成后调用 updateSelectedCategoryName）
    updateSelectedCategoryName()
  }

  const submit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
      if (valid) {
        let params = form.value
        if (props.layer.row) {
          updateForm(params)
        } else {
          addForm(params)
        }
      } else {
        console.log('error submit!', fields)
      }
    })
  }
  const addForm = (params: any) => {
    addCategroy(params).then(res => {
      ElMessage({
        type: 'success',
        message: '新增成功',
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
  }

  const updateForm = (params: updateCategroyParms) => {
    updateCategroy(params).then(res => {
      ElMessage({
        type: 'success',
        message: '操作成功',
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
  }
</script>

<style lang="scss" scoped>
  .category-selector-wrapper {
    position: relative;
    width: 100%;
  }

  .category-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    margin-top: 4px;
    padding: 8px;
  }

  .category-selector {
    .category-tree {
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      background-color: #fff;
      padding: 4px;
    }

    .category-item {
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: background-color 0.2s;
      display: flex;
      align-items: center;
      margin: 1px 0;
      min-height: 28px;

      &:hover {
        background-color: #f5f7fa;
      }
    }

    .category-content {
      display: flex;
      align-items: center;
      width: 100%;
      gap: 4px;
    }

    .category-name {
      flex: 1;
      font-size: 14px;
      color: #606266;

      &.selected {
        color: #409eff;
        font-weight: 500;
      }
    }
  }

  .rotate-180 {
    transform: rotate(180deg);
    transition: transform 0.3s;
  }
</style>
