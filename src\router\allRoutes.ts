import { createNameComponent } from "./createNode";

const allRoutes = {
dashboard: {
    components: {
        basicTemplate: createNameComponent(() => import('@/views/main/dashboard/components/basic-template.vue')),
        card: {
            index: createNameComponent(() => import('@/views/main/dashboard/components/card/index.vue')),
            row: createNameComponent(() => import('@/views/main/dashboard/components/card/row.vue')),
        },
        charts: {
            barChart: createNameComponent(() => import('@/views/main/dashboard/components/charts/barChart.vue')),
            circleChart: createNameComponent(() => import('@/views/main/dashboard/components/charts/circleChart.vue')),
            index: createNameComponent(() => import('@/views/main/dashboard/components/charts/index.vue')),
            modules: {

            },
            pieChart: createNameComponent(() => import('@/views/main/dashboard/components/charts/pieChart.vue')),
        },
    },
    index: createNameComponent(() => import('@/views/main/dashboard/index.vue')),
},
financial: {
    operate: {
        withdrawals: {
            index: createNameComponent(() => import('@/views/main/financial/operate/withdrawals/index.vue')),
            layer: createNameComponent(() => import('@/views/main/financial/operate/withdrawals/layer.vue')),
        },
    },
    record: {
        integral: {
            index: createNameComponent(() => import('@/views/main/financial/record/integral/index.vue')),
        },
        money: {
            index: createNameComponent(() => import('@/views/main/financial/record/money/index.vue')),
        },
        recharge: {
            index: createNameComponent(() => import('@/views/main/financial/record/recharge/index.vue')),
        },
    },
},
maintain: {
    components: {
        getFormId: createNameComponent(() => import('@/views/main/maintain/components/getFormId.vue')),
    },
    devconfig: {
        combineddata: {
            formlayerEdit: createNameComponent(() => import('@/views/main/maintain/devconfig/combineddata/formlayerEdit.vue')),
            fornlayer: createNameComponent(() => import('@/views/main/maintain/devconfig/combineddata/fornlayer.vue')),
            index: createNameComponent(() => import('@/views/main/maintain/devconfig/combineddata/index.vue')),
            layer: createNameComponent(() => import('@/views/main/maintain/devconfig/combineddata/layer.vue')),
        },
        configcategory: {
            configLayer: createNameComponent(() => import('@/views/main/maintain/devconfig/configcategory/configLayer.vue')),
            index: createNameComponent(() => import('@/views/main/maintain/devconfig/configcategory/index.vue')),
            layer: createNameComponent(() => import('@/views/main/maintain/devconfig/configcategory/layer.vue')),
        },
        formconfig: {
            index: createNameComponent(() => import('@/views/main/maintain/devconfig/formconfig/index.vue')),
            layer: createNameComponent(() => import('@/views/main/maintain/devconfig/formconfig/layer.vue')),
        },
    },
},
order: {
    index: createNameComponent(() => import('@/views/main/order/index.vue')),
    layer: createNameComponent(() => import('@/views/main/order/layer.vue')),
},
product: {
    manage: {
        createproduct: createNameComponent(() => import('@/views/main/product/manage/createproduct.vue')),
        editWarehouse: createNameComponent(() => import('@/views/main/product/manage/editWarehouse.vue')),
        index: createNameComponent(() => import('@/views/main/product/manage/index.vue')),
        layer: createNameComponent(() => import('@/views/main/product/manage/layer.vue')),
        selectPrize: createNameComponent(() => import('@/views/main/product/manage/selectPrize.vue')),
        selectSkuPrize: createNameComponent(() => import('@/views/main/product/manage/selectSkuPrize.vue')),
        unitComposition: createNameComponent(() => import('@/views/main/product/manage/unitComposition.vue')),
        unitGroup: createNameComponent(() => import('@/views/main/product/manage/unitGroup.vue')),
        warehouseList: createNameComponent(() => import('@/views/main/product/manage/warehouseList.vue')),
    },
    productSkuList: {
        index: createNameComponent(() => import('@/views/main/product/productSkuList/index.vue')),
    },
    reply: {
        index: createNameComponent(() => import('@/views/main/product/reply/index.vue')),
        layer: createNameComponent(() => import('@/views/main/product/reply/layer.vue')),
    },
    rule: {
        index: createNameComponent(() => import('@/views/main/product/rule/index.vue')),
        layer: createNameComponent(() => import('@/views/main/product/rule/layer.vue')),
    },
    sort: {
        index: createNameComponent(() => import('@/views/main/product/sort/index.vue')),
        layer: createNameComponent(() => import('@/views/main/product/sort/layer.vue')),
    },
},
systemSetting: {
    administratorAuthority: {
        adminList: {
            index: createNameComponent(() => import('@/views/main/systemSetting/administratorAuthority/adminList/index.vue')),
            layer: createNameComponent(() => import('@/views/main/systemSetting/administratorAuthority/adminList/layer.vue')),
        },
        identityManager: {
            index: createNameComponent(() => import('@/views/main/systemSetting/administratorAuthority/identityManager/index.vue')),
            layer: createNameComponent(() => import('@/views/main/systemSetting/administratorAuthority/identityManager/layer.vue')),
        },
        index: createNameComponent(() => import('@/views/main/systemSetting/administratorAuthority/index.vue')),
        permissionRules: {
            index: createNameComponent(() => import('@/views/main/systemSetting/administratorAuthority/permissionRules/index.vue')),
            layer: createNameComponent(() => import('@/views/main/systemSetting/administratorAuthority/permissionRules/layer.vue')),
        },
    },
    logistics: {
        index: createNameComponent(() => import('@/views/main/systemSetting/logistics/index.vue')),
        layer: createNameComponent(() => import('@/views/main/systemSetting/logistics/layer.vue')),
    },
    picture: {
        index: createNameComponent(() => import('@/views/main/systemSetting/picture/index.vue')),
    },
    setting: {
        index: createNameComponent(() => import('@/views/main/systemSetting/setting/index.vue')),
    },
},
user: {
    usermanage: {
        index: createNameComponent(() => import('@/views/main/user/usermanage/index.vue')),
        updataMoneyLayer: createNameComponent(() => import('@/views/main/user/usermanage/updataMoneyLayer.vue')),
    },
},
}

export default allRoutes;
