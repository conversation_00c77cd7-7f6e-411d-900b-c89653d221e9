<template>
  <div class="layout-container" style="padding: 15px">
    <el-tabs v-model="query.type" class="demo-tabs" @tab-click="clickTab">
      <el-tab-pane
        :label="item.name + `(${item.count})`"
        :name="item.type"
        v-for="item in tabsList"
        :key="item.type"
      ></el-tab-pane>
    </el-tabs>
    <div style="text-align: left">
      <el-form label-width="100px" :inline="false">
        <el-form-item label="库存筛选">
          <el-radio-group v-model="stockFilter" @change="handleStockFilterChange">
            <el-radio-button
              v-for="item in stockFilterOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label-width="0">
          <el-form-item label="商品分类">
            <el-cascader
              :options="options"
              :props="props"
              clearable
              v-model="query.cateId"
              @change="getTableData(true)"
            />
          </el-form-item>
          <el-form-item label="商品搜索">
            <div class="layout-container-form-search">
              <el-input
                v-model="query.keywords"
                placeholder="请输入关键字"
                clearable
                @clear="getTableData(true)"
              ></el-input>
              <el-button
                type="primary"
                :icon="Search"
                class="search-btn"
                @click="getTableData(true)"
              >
                {{ $t('message.common.search') }}
              </el-button>
            </div>
          </el-form-item>
        </el-form-item>
        <!--<el-form-item label="商品标识">
                    <el-checkbox v-model="query.isHot" label="热卖" />
                </el-form-item>-->
      </el-form>
    </div>
    <div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ $t('message.common.add') }}
        </el-button>
      </div>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <!--<el-table-column type="expand">
                    <template #default="props">
                        <el-descriptions>
                            <el-descriptions-item label="所属分区">{{ props.row.cateValues }}</el-descriptions-item>
                            <el-descriptions-item label="市场价">{{ props.row.otPrice }}</el-descriptions-item>
                            <el-descriptions-item label="成本价">{{ props.row.otPrice }}</el-descriptions-item>
                            <el-descriptions-item label="虚拟销量">{{ props.row.ficti }}</el-descriptions-item>
                        </el-descriptions>
                    </template>
                </el-table-column>-->
        <el-table-column prop="id" label="id" align="center" width="80" />
        <el-table-column label="商品图" align="center">
          <template #default="scope">
            <!-- <img src="" alt="" srcset=""> -->
            <imagePreview :imgurl="scope.row.image"></imagePreview>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" align="center" />
        <el-table-column prop="groupName" label="产品规格" align="center" />
        <!--<el-table-column prop="price" label="售价" align="center" />-->
        <el-table-column
          prop="unitGroupStr"
          label="库存"
          align="center"
        >
          <template #default="scope">
            <div class="newline-text">
              {{ scope.row.unitGroupStr }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="minUnitStock" label="数量" align="center" />
        <el-table-column prop="sort" label="排序" align="center" />
        <!--<el-table-column prop="status" label="状态" align="center" v-if="query.type!==5">
                    <template #default="scope">
                        <span class="statusName">{{ scope.row.isShow ? "上架" : "下架" }}</span>
                        <el-switch v-model="scope.row.isShow" active-color="#13ce66" inactive-color="#ff4949"
                            :active-value="true" :inactive-value="false" :loading="scope.row.loading"
                            @change="updateStatus(scope.row)"></el-switch>
                    </template>
                </el-table-column>-->

        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="280"
        >
          <template #default="scope">
            <el-button
              @click="restore(scope.row.id)"
              type="success"
              v-if="query.type == 5"
            >
              恢复商品
            </el-button>
            <el-button @click="handleEdit(scope.row.id)">
              {{ $t('message.common.update') }}
            </el-button>
            <el-button @click="handleEditWarehouse(scope.row.id)">
              {{ $t('message.common.editWarehouse') }}
            </el-button>
            <el-popconfirm
              :title="$t('message.common.delTip')"
              @confirm="handleDel(scope.row.id)"
              v-if="query.type != 5"
            >
              <template #reference>
                <el-button type="danger">
                  {{ $t('message.common.del') }}
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </Table>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { defineComponent, ref, reactive, onMounted } from 'vue'
  import { Page } from '@/components/table/type'
  import { LayerInterface } from '@/components/layer/index.vue'
  import { ElMessage } from 'element-plus'
  import {
    getProductList,
    getTabsHearder,
    putOnShell,
    offShell,
    deleteProduct,
    restoreProduct,
  } from '@/api/product'
  import { treeCategroy } from '@/api/categoryApi'
  import Table from '@/components/table/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import { Plus, Delete, Search } from '@element-plus/icons-vue'
  import type { TabsPaneContext } from 'element-plus'
  import type { ProductHeaderRes } from '@/type/product.type'
  import { useRouter, useRoute } from 'vue-router'
  import { TreeCategroyRes } from '@/type/category.type'
  import type { ProductListParams, ProductListRes } from '@/type/product.type'
  const router = useRouter()
  const route = useRoute()

  // 库存筛选状态
  const stockFilter = ref('')

  // 库存筛选选项
  const stockFilterOptions = [
    { label: '全部', value: '' },
    { label: '警戒', value: 'stockLack' }
  ]

  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
    type: 1,
    cateId: '',
    isHot: false,
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增商品',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<ProductListRes[]>([])

  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: ProductListParams = {
      ...query,
      page: page.index,
      limit: page.size,
    }

    // 添加库存筛选参数
    if (stockFilter.value) {
      params.extra = stockFilter.value
    }

    getProductList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  // 库存筛选处理函数
  const handleStockFilterChange = () => {
    getTableData(true)
  }

  // 删除功能
  const handleDel = (data: number) => {
    deleteProduct(data).then(res => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTabsHeader
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  // 新增弹窗功能
  const handleAdd = () => {
    // layer.title = "新增数据";
    // layer.show = true;
    // delete layer.row;
    router.push({ path: '/product/createproduct' })
  }
  // 编辑弹窗功能
  const handleEdit = (id: number) => {
    router.push({ path: '/product/createproduct', query: { data: id } })
  }

  const handleEditWarehouse = (id: number) => {
    router.push({ path: '/product/editWarehouse', query: { data: id } })
  }

  const updateStatus = (row: ProductListRes) => {
    if (row.isShow) {
      putOnShell(row.id!).then(res => {
        ElMessage({
          type: 'success',
          message: '上架成功',
        })
        getTableData(true)
        // getTabsHeader
      })
    } else {
      offShell(row.id!).then(res => {
        ElMessage({
          type: 'success',
          message: '下架成功',
        })
        getTableData(true)
        getTabsHeader()
      })
    }
  }
  const tabsList = ref<ProductHeaderRes[]>([])

  //切换tab
  const clickTab = (tab: TabsPaneContext, event: Event) => {
    getTableData(true)
  }
  //商品分类选择框
  const options = ref<TreeCategroyRes[]>([])
  const props = {
    value: 'id',
    label: 'name',
    children: 'child',
    emitPath: false,
  }
  treeCategroy({ type: 1, status: -1 }).then(res => [(options.value = res)])

  //恢复商品
  const restore = (id: number) => {
    restoreProduct(id).then(res => {
      ElMessage({
        type: 'success',
        message: '操作成功',
      })
      getTabsHeader()
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  const getTabsHeader = () => {
    /*getTabsHearder().then(res => {
            tabsList.value = res
            getTableData(true)
        })*/
    getTableData(true)
  }
  // 初始化库存筛选状态
  onMounted(() => {
    // 从路由参数中读取extra值
    const extraParam = route.query.extra as string
    if (extraParam) {
      stockFilter.value = extraParam
    }

    // 在设置完筛选状态后再获取数据
    getTabsHeader()
  })
</script>

<style lang="scss" scoped>
  .newline-text {
    white-space: pre-line;
  }
  .statusName {
    margin-right: 10px;
  }

  .picture {
    width: 30px;
    height: 30px;
  }
</style>
