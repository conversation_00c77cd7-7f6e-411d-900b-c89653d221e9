
import request from '@/utils/system/request'
/**
 * 图片上传
 * @param data
 */
export function fileImageApi (data, params) {
  return request({
    url: `/api/admin/upload/image`,
    method: 'POST',
    params,
    data
  })
}

/**
 * 图片列表
 * @param data
 */
export function fileListApi (params) {
  return request({
    url: '/api/admin/system/attachment/list',
    method: 'get',
    params
  })
}
/**
 * 图片列表 删除图片
 * @param data
 */
export function fileDeleteApi (id) {
  return request({
    url: `/api/admin/system/attachment/delete/${id}`,
    method: 'get'
  })
}
/**
 * 图片列表 移動分類
 * @param data
 */
export function attachmentMoveApi (data) {
  return request({
    url: `/api/admin/system/attachment/move`,
    method: 'post',
    data
  })
}
