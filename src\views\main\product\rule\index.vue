<template>
  <div class="layout-container">
    <div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ $t('message.common.add') }}
        </el-button>
        <el-popconfirm
          :title="$t('message.common.delTip')"
          @confirm="handleDel(chooseData)"
        >
          <template #reference>
            <el-button
              type="danger"
              :icon="Delete"
              :disabled="chooseData.length === 0"
            >
              {{ $t('message.common.delBat') }}
            </el-button>
          </template>
        </el-popconfirm>
      </div>
      <div class="layout-container-form-search">
        <el-input
          v-model="query.keywords"
          :placeholder="$t('message.common.searchTip')"
          @change="getTableData(true)"
        ></el-input>
        <el-button
          type="primary"
          :icon="Search"
          class="search-btn"
          @click="getTableData(true)"
        >
          {{ $t('message.common.search') }}
        </el-button>
      </div>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :showSelection="true"
        :data="tableData"
        @getTableData="getTableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column prop="id" label="id" align="center" />
        <el-table-column prop="ruleName" label="规则名称" align="center" />
        <el-table-column prop="ruleValue" label="规格属性" align="center">
          <template #default="scope">
            <div v-for="(item, index) in getRuleValue(scope.row.ruleValue)">
              {{ item }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)">
              {{ $t('message.common.update') }}
            </el-button>
            <el-popconfirm
              :title="$t('message.common.delTip')"
              @confirm="handleDel([scope.row])"
            >
              <template #reference>
                <el-button type="danger">
                  {{ $t('message.common.del') }}
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </Table>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { defineComponent, ref, reactive } from 'vue'
  import Table from '@/components/table/index.vue'
  import { Page } from '@/components/table/type'
  import Layer from './layer.vue'
  import { ElMessage } from 'element-plus'
  import type { LayerInterface } from '@/components/layer/index.vue'
  import { Plus, Search, Delete } from '@element-plus/icons-vue'
  import { getProductRuleList, delProductRule } from '@/api/product'
  import type { RuleListParams, RuleListRes } from '@/type/product.type'
  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<RuleListRes[]>([])
  const chooseData = ref([])
  const handleSelectionChange = (val: []) => {
    chooseData.value = val
  }
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: RuleListParams = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getProductRuleList(params)
      .then(res => {
        let data = res.list
        tableData.value = res.list
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  // 删除功能
  const handleDel = (data: RuleListRes[]) => {
    let params = {
      ids: data
        .map(e => {
          return e.id
        })
        .join(','),
    }
    delProductRule(params.ids).then(res => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  // 新增弹窗功能
  const handleAdd = () => {
    layer.title = '新增数据'
    layer.show = true
    delete layer.row
  }
  // 编辑弹窗功能
  const handleEdit = (row: RuleListRes) => {
    layer.title = '编辑数据'
    layer.row = row
    layer.show = true
  }
  getTableData(true)
  /* 处理列表中的规格详情 */
  const getRuleValue = (row: any) => {
    if (!row) return
    let data = JSON.parse(row)
    let newData = data.map(
      (item: { value: string; detail: { toString: () => string } }) => {
        let str = ''
        str += item.value + ':'
        str += item.detail.toString()
        return str
      }
    )
    return newData
  }
</script>

<style lang="scss" scoped></style>
