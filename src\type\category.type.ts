/**
 * Category对象，分类表
 */
export interface CategroyParms {
  /**
   * @name 扩展字段
   */
  extra?: string
  /**
   * @name 分类名称
   */
  name: string
  /**
   * @name 父级ID
   */
  pid: number
  /**
   * @name 排序
   */
  sort: number
  /**
   * @name 状态, 0正常，1失效
   */
  status: boolean
  /**
   * @name 类型，类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置
   */
  type: number
  /**
   * @name 地址
   */
  url?: string
}

/**分类数据tree数据 传参类型 */
export interface TreeCategroyParms {
  /**
   * @name 模糊搜索
   */
  name?: string
  /**
   * @name -1=全部，0=未生效，1=已生效
   */
  status: number
  /**
   * @name 类型ID | 类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置
   */
  type: number
}

/**分类数据tree数据 传参类型 */
export interface updateCategroyParms extends CategroyParms {
  /**
   * @name ID
   */
  id: number
}

// 返回类型

/** 分类详情  */
export interface infoCategroyRes {
  /**
   * @name 扩展字段
   */
  extra?: string
  /**
   * @name 分类名称
   */
  name: string
  /**
   * @name 父级ID
   */
  pid: number
  /**
   * @name 排序
   */
  sort: number
  /**
   * @name 状态, 0正常，1失效
   */
  status: boolean
  /**
   * @name 类型，类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置
   */
  type: number
  /**
   * @name 地址
   */
  url?: string
}

/**
 * 分类数据tree数据
 */
export interface TreeCategroyRes {
  child?: TreeCategroyRes[]
  /**
   * @name 扩展字段
   */
  extra?: string
  id?: number
  /**
   * @name 分类名称
   */
  name?: string
  /**
   * @name 路径
   */
  path?: string
  /**
   * @name 父级ID
   */
  pid?: number
  /**
   * @name 排序
   */
  sort?: number
  /**
   * @name 状态, 0正常，1失效
   */
  status?: boolean
  /**
   * @name 类型，类型，1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置
   */
  type?: number
  /**
   * @name 地址
   */
  url?: string
  loading?: boolean
}
