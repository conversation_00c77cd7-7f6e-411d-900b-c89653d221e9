# 版本更新日志
## 1.0.4版本
1. 更新element-plus至最新版本

## 1.0.3版本
1. 新增路由菜单配置功能说明
2. 新增中国水墨风主题（待优化）
3. 优化多级菜单的使用方法

## 1.0.2版本
1. 增加文档说明

## 1.0.1版本
1. 优化tab模块的逻辑
2. 优化菜单问题

## 1.0版本
1. 修复已知的BUG
2. 完善使用体验

## 0.41版本
1. 【修改】菜单过长时，滚动条问题
2. 【新增】主题配置功能，核心编辑代码编写

## 0.40版本
1. 【优化】路由keep-alive状态保存与删除
2. 【新增】当前页刷新，缓存页刷新机制
3. 【新增】关闭标签栏时的缓存页对应状态变化

## 0.32版本
1. 【优化】左侧菜单栏复杂情况下的示例应用
2. 【新增】分类联动表格的实现
3. 【新增】树联动表格的实现
4. 【BUG】表格+开发BUG修复

## 0.31版本
1. 【新增】指令集模块完成
2. 【新增】echarts图表模块完成

## 0.24版本
```
1. 【新增】弹窗截图功能
```

## 0.23版本
```
1. 【新增】代码编辑器
2. 【新增】JSON编辑器
3. 【新增】可拖拽面板
4. 【新增】地图组件
```

## 0.22版本
```
1. 【新增】MD编辑器
```

## 0.21版本
```
1. 【统计】统计大部分需要实现的功能页面，并展示于左侧菜单中
2. 【新增】新增大部分需要实现的路由，并确保，大部分能于7月1日之前完全实现
```

## 0.11版本
```
1. 【新增】新增组件栏目，并补充按钮组进入
2. 【优化】多级菜单跳转demo
3. 【优化】侧边栏功能优化
```

## 0.10版本
```
1. 【新增】实现多级菜单Demo
2. 【优化】当菜单数据量过高时，优化显示
```

## 0.9版本
```
1. 【优化】弹窗组件可拖拽
2. 【优化】弹窗组件内部暴露逻辑，供外部使用
```

## 0.8版本
```
1. 【优化】axios提示配置
2. 【优化】公用组件内部逻辑及外部调用方法
3. 【补充】业务表格模块的数据调用方式
```

## 0.7版本
```
1. 【优化】element-ui国际化配置指南
2. 【优化】国际化配置本地存储
```

## 0.6版本
```
1. 【新增】按钮尺寸调整功能，针对element-plus的全局尺寸调整
2. 【新增】首页，图表类展示
3. 【新增】页面模块，目前包含了业务表格，主要为较为完善的crud写法，正在完善中
4. 【新增】基于echarts封装的业务chart组件，用户可快速利用此组件生成需要的图表
```

## 0.5.1版本
```
1. 【修复】mock地址，尽量不要使用较短的url,否则，打包上线后，较长的URL容易被冲突掉，示例：登录url和退出登录URL
```

## 0.5版本
```
1. 【优化】本地mock地址使用细节补充及细节优化
2. 【新增】登录页鉴权
3. 【新增】退出登录功能
4. 【优化】本地封装的axios插件优化使用方案
```

## 0.4版本

```
1. 【新增】登录页面,及手机自适应处理
2. 【新增】axios插件，请求处理机制建立
3. 【优化】全局/@/替换为@/
4. 【新增】本地mock地址模拟
```

## 0.3.1版本

```
1. 【实现】面包屑导航三种关闭功能实现
```

## 0.3版本

```
1. 【新增】github跳转链接
2. 【新增】主题配置功能[页面制作实现]
3. 【新增】vuex持久化插件，手写版
```

## 0.2版本

```
1. 【新增】框架国际化处理
2. 【BUG】打包上线上路由跳转功能异常，已修复
```



## 0.1版本

```
1. 集成vue-router，vuex，@vueuse/core，element-plus等核心插件
2. 全局状态管理方案实现
3. 全局路由管理方案实现
4. 核心组件库引入处理
5. 全局layout制作，及自适应处理
6. 菜单解决方案实现
7. 面包屑导航实现
8. 全屏功能实现
```

