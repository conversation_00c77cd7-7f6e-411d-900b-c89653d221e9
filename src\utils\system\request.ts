import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from "axios";
import store from '@/store'
import { ElMessage } from 'element-plus'

// 创建 Axios 实例
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 5000,
  headers: { "Content-Type": "application/json" }
});

// 定义请求响应类型
interface ApiResponse<T> {
  data?: T;
  code: number;
  message?: string;
}

// 请求方法封装
const request = async <T>(config: AxiosRequestConfig): Promise<ApiResponse<T>> => {
  const response = await api.request<ApiResponse<T>>(config);
  if (response.data.code !== 200) {
    showError(response.data);
    throw new Error(response.data.message);
  }
  return response.data;
};

// 请求拦截器
api.interceptors.request.use(
  (config: AxiosRequestConfig) => {
    config = config || {};
    config.headers = config.headers || {};
    if (store.getters['user/token']) {
      config.headers['adminToken'] = store.getters['user/token']
    }
    if (config.params && (typeof (config.params) === 'object')) {
      config.params = Object.fromEntries(Object.entries(config.params)
        .filter(([_, value]) => value !== '' ));
    }
    // 添加请求头，验证 token 等
    return config;
  },
  (error) => Promise.reject(error)
);
let TokenInvalidFlag = 0
// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // 处理响应数据，例如分页、状态码等
    if (response.data.code === 200) {
      TokenInvalidFlag = 0
      return response;
    } else {
      showError(response.data)
      return Promise.reject(response.data)
    }

  },
  (error) => {
    // 处理响应异常
    console.error(error);
    showError({ code:error.response?.data.code, message: error.response?.data.message })
    return Promise.reject(error);
  }
);

function showError(error: any) {
  // token过期，清除本地数据，并跳转至登录页面
  if (error.code === 401 ) {
    if (TokenInvalidFlag === 0) {
      store.dispatch("user/loginOut")
      TokenInvalidFlag = 1
      ElMessage({
        message: error.msg || error.message,
        type: "error",
        duration: 3 * 1000,
      })
    }

  } else {
    ElMessage({
      message: error.message || "服务异常",
      type: "error",
      duration: 3 * 1000,
    })
  }
}
// 统一请求方法封装
const httpRequest = async <T>(config: AxiosRequestConfig): Promise<T> => {
  const response = await request<T>(config);
  return response.data!;
};

export default httpRequest