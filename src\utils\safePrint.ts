/**
 * 安全打印工具 - 使用 print-js 库解决Edge浏览器打印问题
 *
 * 主要功能：
 * 1. 使用 print-js 库提供稳定的打印功能
 * 2. 支持 PDF、HTML、图片等多种格式打印
 * 3. 自动处理用户激活和浏览器兼容性问题
 * 4. 提供统一的错误处理和调试支持
 */

interface SafePrintOptions {
  /** 是否显示打印加载提示，默认true */
  showModal?: boolean
  /** 打印页面标题 */
  documentTitle?: string
  /** 是否显示用户激活提示 */
  showActivationTip?: boolean
  /** print-js 的额外配置 */
  printJSOptions?: any
  /** 打印质量优化选项 */
  qualityOptions?: {
    /** 是否启用高质量模式，默认true */
    highQuality?: boolean
    /** 图片最大宽度，默认不限制 */
    maxImageWidth?: number
    /** 是否保持颜色精确度，默认true */
    preserveColors?: boolean
    /** 自定义DPI设置提示 */
    dpiHint?: string
  }
}

/**
 * 检查用户激活状态
 */
export function checkUserActivation(): boolean {
  if (navigator.userActivation) {
    return navigator.userActivation.hasBeenActive
  }
  // 对于不支持 userActivation 的浏览器，假设已激活
  return true
}

/**
 * 获取优化的打印配置
 */
function getOptimizedPrintConfig(options: SafePrintOptions) {
  const { qualityOptions = {} } = options
  const {
    highQuality = true,
    maxImageWidth,
    preserveColors = true,
    dpiHint = '建议在打印对话框中选择"高质量"或"最佳"打印质量'
  } = qualityOptions

  // 基础优化配置
  const optimizedConfig: any = {}

  if (highQuality) {
    // 高质量打印的CSS样式
    optimizedConfig.style = `
      @media print {
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        body {
          font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
          font-size: 12pt;
          line-height: 1.4;
        }
        img {
          max-width: ${maxImageWidth ? maxImageWidth + 'px' : '100%'} !important;
          height: auto !important;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
        }
        table {
          border-collapse: collapse;
          font-size: 11pt;
        }
        th, td {
          border: 1px solid #000;
          padding: 4px 6px;
        }
      }
    `
  }

  // 在控制台显示DPI提示
  if (dpiHint && highQuality) {
    console.info(`[SafePrint] 打印质量提示: ${dpiHint}`)
  }

  return optimizedConfig
}

/**
 * 安全打印PDF文件 - 完整版本，解决弹窗自动关闭问题
 * @param pdfBlob PDF文件的Blob对象
 * @param options 打印选项
 */
export async function safePrintPDF(pdfBlob: Blob, options: SafePrintOptions = {}) {
  const {
    showModal = true,
    documentTitle = '打印文档',
    showActivationTip = true,
    printJSOptions = {},
    qualityOptions = {}
  } = options

  // 获取优化配置
  const optimizedConfig = getOptimizedPrintConfig(options)

  return new Promise<void>(async (resolve, reject) => {
    try {
      // 检查用户激活状态
      if (!checkUserActivation()) {
        const message = '请先点击页面任意位置，然后重新打印'
        if (showActivationTip) {
          console.warn(message)
        }
        reject(new Error(message))
        return
      }

      // 动态导入 print-js
      const { default: printJS } = await import('print-js')

      // 创建PDF的URL
      const pdfUrl = window.URL.createObjectURL(pdfBlob)

      // 使用 print-js 打印PDF，合并优化配置
      printJS({
        printable: pdfUrl,
        type: 'pdf',
        showModal: showModal,
        modalMessage: '正在准备打印文档...',
        documentTitle: documentTitle,
        onPrintDialogClose: () => {
          // 清理资源
          window.URL.revokeObjectURL(pdfUrl)
          resolve()
        },
        onError: (error: any) => {
          console.error('[SafePrint] print-js 打印失败:', error)
          window.URL.revokeObjectURL(pdfUrl)
          reject(new Error('打印失败: ' + error.message))
        },
        ...optimizedConfig,
        ...printJSOptions
      })

    } catch (error) {
      console.error('[SafePrint] PDF打印过程出错:', error)
      reject(error)
    }
  })
}

/**
 * 安全打印HTML内容 - 使用 print-js 库
 * @param html HTML字符串
 * @param options 打印选项
 */
export async function safePrintHTML(html: string, options: SafePrintOptions = {}) {
  const {
    showModal = true,
    documentTitle = '打印文档',
    showActivationTip = true,
    printJSOptions = {}
  } = options

  // 获取优化配置
  const optimizedConfig = getOptimizedPrintConfig(options)

  return new Promise<void>(async (resolve, reject) => {
    try {
      // 检查用户激活状态
      if (!checkUserActivation()) {
        const message = '请先点击页面任意位置，然后重新打印'
        if (showActivationTip) {
          console.warn(message)
        }
        reject(new Error(message))
        return
      }

      // 动态导入 print-js
      const { default: printJS } = await import('print-js')

      // 使用 print-js 打印HTML，合并优化配置
      printJS({
        printable: html,
        type: 'raw-html',
        showModal: showModal,
        modalMessage: '正在准备打印文档...',
        documentTitle: documentTitle,
        onPrintDialogClose: () => {
          resolve()
        },
        onError: (error: any) => {
          console.error('[SafePrint] print-js HTML打印失败:', error)
          reject(new Error('HTML打印失败: ' + error.message))
        },
        ...optimizedConfig,
        ...printJSOptions
      })

    } catch (error) {
      console.error('[SafePrint] HTML打印过程出错:', error)
      reject(error)
    }
  })
}

/**
 * 打印当前窗口（需要用户激活）
 */
export function safePrintWindow(options: SafePrintOptions = {}) {
  const { showActivationTip = true } = options

  return new Promise<void>((resolve, reject) => {
    // 检查用户激活状态
    if (!checkUserActivation()) {
      const message = '请先点击页面任意位置，然后重新打印'
      if (showActivationTip) {
        console.warn(message)
      }
      reject(new Error(message))
      return
    }

    try {
      // 监听打印完成事件
      const handleAfterPrint = () => {
        window.removeEventListener('afterprint', handleAfterPrint)
        resolve()
      }
      
      window.addEventListener('afterprint', handleAfterPrint)
      window.print()
      
    } catch (error) {
      reject(error)
    }
  })
}

/**
 * 高质量打印PDF - 专门优化清晰度的版本
 * @param pdfBlob PDF文件的Blob对象
 * @param options 打印选项
 */
export async function safePrintPDFHighQuality(pdfBlob: Blob, options: Omit<SafePrintOptions, 'qualityOptions'> = {}) {
  return safePrintPDF(pdfBlob, {
    ...options,
    qualityOptions: {
      highQuality: true,
      preserveColors: true,
      dpiHint: '🎯 高质量模式已启用！建议在打印对话框中选择"高质量"或"最佳"打印质量以获得最佳效果'
    }
  })
}

/**
 * 高质量打印HTML - 专门优化清晰度的版本
 * @param html HTML字符串
 * @param options 打印选项
 */
export async function safePrintHTMLHighQuality(html: string, options: Omit<SafePrintOptions, 'qualityOptions'> = {}) {
  return safePrintHTML(html, {
    ...options,
    qualityOptions: {
      highQuality: true,
      maxImageWidth: 1200, // 限制图片最大宽度以保持清晰度
      preserveColors: true,
      dpiHint: '🎯 高质量模式已启用！建议在打印对话框中选择"高质量"或"最佳"打印质量以获得最佳效果'
    }
  })
}

/**
 * 获取浏览器信息（用于调试）
 */
export function getBrowserInfo() {
  const userAgent = navigator.userAgent
  const isEdge = /Edg/.test(userAgent)
  const isChrome = /Chrome/.test(userAgent) && !isEdge
  const isFirefox = /Firefox/.test(userAgent)
  const isSafari = /Safari/.test(userAgent) && !isChrome && !isEdge

  return {
    userAgent,
    isEdge,
    isChrome,
    isFirefox,
    isSafari,
    supportsUserActivation: 'userActivation' in navigator,
    hasBeenActive: navigator.userActivation?.hasBeenActive ?? true
  }
}
