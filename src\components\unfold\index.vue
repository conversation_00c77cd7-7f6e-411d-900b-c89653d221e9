<template>
    <div>
            <div v-show="showSearch">
                <slot></slot>
            </div>
        <el-form-item>
            <el-button class="search-btn" @click="showSearch = !showSearch">
                <div style="display: flex; align-items: center;" v-show="!showSearch"><el-icon>
                        <ArrowDown />
                    </el-icon>展开</div>
                <div v-show="showSearch"><el-icon>
                        <ArrowUp />
                    </el-icon>收起</div>
            </el-button>
        </el-form-item>
    </div>
</template>

<script setup lang="ts">
import { ref } from "vue"
const showSearch = ref(false)
</script>
<style lang="scss" scoped>
</style>
