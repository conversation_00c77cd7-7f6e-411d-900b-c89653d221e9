@import './transition.scss';
@import '@/theme/index.scss';
.inputWidth {
  width: 185px;
}
.layout-container {
  background-color: var(--system-container-main-background);
  width: calc(100% - 30px);
  height: calc(100% - 30px);
  margin: 15px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  &-form {
    display: flex;
    justify-content: space-between;
    padding: 15px 15px 0;
    &-handle {
      display: flex;
      justify-content: flex-start;
      .export-excel-btn {
        margin-left: 15px;
      }
    }
    &-search {
      display: flex;
      justify-content: flex-end;
      .search-btn {
        margin-left: 15px;
      }
    }
    .el-form-item {
      margin-bottom: 0;
    }
  }
  &-table {
    flex: 1;
    height: 100%;
    padding: 15px;
    overflow: auto;
  }
}
.flex-box {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  padding: 15px;
  box-sizing: border-box;
}
.flex {
  display: flex;
}
.center {
  justify-content: center;
  align-items: center;
  text-align: center;
}
a {
  text-decoration: none;
}

/** element-plus **/
.el-icon {
  text-align: center;
}

/** 用于提示信息 **/
.my-tip {
  background-color: #f1f1f1;
  padding: 5px 10px;
  text-align: left;
  border-radius: 4px;
}

/** 自定义动画 **/
@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-down {
  animation: fadeInDown 0.3s ease-out;
}
.system-scrollbar {
  &::-webkit-scrollbar {
    display: none;
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: rgba(144, 147, 153, 0.3);
  }
  &:hover {
    &::-webkit-scrollbar {
      display: block;
    }
    &::-webkit-scrollbar-thumb {
      border-radius: 10px;
      background: rgba(144, 147, 153, 0.3);
      &:hover {
        background: rgba(144, 147, 153, 0.5);
      }
    }
  }
}

/* 全局右键菜单样式 - v-contextmenu 组件挂载在 app 同级，需要全局样式 */
.v-contextmenu {
  min-width: 180px !important;
  width: 180px !important;
  background: white !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  box-shadow:
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
  padding: 4px 0 !important;
  font-size: 14px !important;
  z-index: 1000 !important;
}

.v-contextmenu-item {
  border-radius: 4px !important;
  width: auto !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  color: #374151 !important;
  transition: all 0.15s ease-in-out !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  background-color: transparent !important;

  &:hover {
    background-color: #f3f4f6 !important;
    color: #111827 !important;
  }

  .el-icon {
    margin-right: 8px !important;
    width: 16px !important;
    height: 16px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    flex-shrink: 0 !important;
  }
}

/* 重写 v-contextmenu-item--hover 类的背景色 */
.v-contextmenu-item--hover {
  background-color: #efefef !important;
  color: #111827 !important;
}

/* 删除操作的特殊样式 */
.v-contextmenu-item.delete-item {
  color: #dc2626 !important;
}
