export default (Vue: { directive: (arg0: string, arg1: { mounted(el: any, binding: any): void }) => void; config: { globalProperties: { $_has: (arg0: any) => any } } }) => {
  /**自定义按钮权限指令 */
  Vue.directive('has', {
    mounted(el: { parentNode: { removeChild: (arg0: any) => void } }, binding: { value: any }) {
      console.log(el, binding)
      //获取按钮权限
      if (!Vue.config.globalProperties.$_has(binding.value)) {
        //移除不匹配的按钮
        el.parentNode.removeChild(el)
      }
    },
  })
  //检查权限方法
  Vue.config.globalProperties.$_has = function (value: string) {
    let isExist = false

    var btnPermsArr = ['user:add', 'user:edit', 'user:del'] //获取从服务器请求存储本地的按钮权限
    if (btnPermsArr.includes(value)) {
      isExist = true
    }
    return isExist
  }
}

