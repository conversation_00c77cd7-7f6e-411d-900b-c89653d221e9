# Dependencies
node_modules/
dist/
dist-ssr/

# Build outputs
*.min.js
*.min.css

# Lock files
package-lock.json
yarn.lock
pnpm-lock.yaml

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Environment files
.env
.env.local
.env.development
.env.production

# Auto-generated files
auto-imports.d.ts
components.d.ts

# Public assets
public/

# Specific project files
proxyConfig.json
