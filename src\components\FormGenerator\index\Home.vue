<template>
  <div class="container-FromGen">
    <div class="left-board">
      <div class="logo-wrapper">
        <div class="logo">
          <!-- <span>CRMEB</span> -->
        </div>
      </div>
      <el-scrollbar class="left-scrollbar">
        <div class="components-list">
          <div v-for="(item, listIndex) in leftComponents" :key="listIndex">

            <div class="components-title">
              <!-- <svg-icon icon-class="component" /> -->
              {{ item.title }}
            </div>
            <draggable class="components-draggable" v-model="item.list"
              :group="{ name: 'componentsGroup', pull: 'clone', put: false }" :clone="cloneComponent" itemKey="id"
              draggable=".components-item" :sort="false" @onEnd="onEnd">
              <template #item="{ element }">
                <div class="components-item" @click="addComponent(element)">
                  <div class="components-body">
                    <i :class="element.__config__.tagIcon"></i>
                    {{ element.__config__.label }}
                  </div>
                </div>
              </template>
            </draggable>
          </div>
        </div>
      </el-scrollbar>
    </div>
    <div class="center-board">
      <div class="action-bar">
        <el-form ref="selfFormRef" inline :model="selfForm">
          <el-form-item label="名称" prop="name"
            :rules="[{ required: true, message: '请填写名称', trigger: ['blur', 'change'] }]">
            <el-input v-model="selfForm.name" placeholder="名称" />
          </el-form-item>
          <el-form-item label="描述" prop="info"
            :rules="[{ required: true, message: '请填写描述', trigger: ['blur', 'change'] }]">
            <el-input v-model="selfForm.info" placeholder="描述" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handlerSaveJSON(selfFormRef)">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
      <el-scrollbar class="center-scrollbar">
        <el-row class="center-board-row" :gutter="formConf.gutter">
          <el-form size="large" :label-position="formConf.labelPosition" :disabled="formConf.disabled"
            :label-width="formConf.labelWidth + 'px'">
            <draggable class="drawing-board" :list="drawingList" :animation="340" group="componentsGroup">
              <template #item="{ element }">
                <draggable-item v-bind="$attrs" :key="element.renderKey" :drawing-list="drawingList" :element="element"
                  :index="data.index" :active-id="activeId" :form-conf="data.formConf" @activeItem="activeFormItem"
                  @deleteItem="drawingItemDelete" />
              </template>
            </draggable>
            <div v-show="!drawingList.length" class="empty-info">
              从左侧拖入或点选组件进行表单设计
            </div>
          </el-form>
        </el-row>
      </el-scrollbar>
    </div>
    <right-panel :active-data="activeData" :form-conf="data.formConf" :show-field="!!drawingList.length"
      @tag-change="tagChange" />
  </div>
</template>

<script setup>
import draggable from 'vuedraggable'
import DraggableItem from './DraggableItem.vue'
import RightPanel from './RightPanel.vue'
import { ref, reactive, toRefs, onMounted, watch, nextTick } from "vue";
import {
  inputComponents, selectComponents, layoutComponents, formConf
} from '@/components/FormGenerator/components/generator/config'
import drawingDefalut from '@/components/FormGenerator/components/generator/drawingDefalut'
import {
  getDrawingList, saveDrawingList, getIdGlobal, saveIdGlobal, getFormConf, getFormConfSelf
} from '@/utils/db'
const idGlobal = getIdGlobal()
const selfFormRef = ref()
let tempActiveData
const props = defineProps({
  editData: {
    type: Object,
    default: {}
  },
  isCreate: {
    type: Number,
    default: 0 // 0=创建，1=编辑
  }
})

const emit = defineEmits(['getFormConfigDataResult ']);
const data = reactive({
  leftComponents: [
    {
      title: '输入型组件',
      list: inputComponents
    },
    {
      title: '选择型组件',
      list: selectComponents
    }
  ],
  selfForm: {
    name: null,
    info: null,
    id: null
  },
  formConf,
  drawingList: [
    {
      __config__: {
        label: '单行文本',
        labelWidth: null,
        showLabel: true,
        changeTag: true,
        tag: 'el-input',
        tagIcon: 'input',
        defaultValue: undefined,
        required: true,
        layout: 'colFormItem',
        span: 24,
        document: 'https://element.eleme.cn/#/zh-CN/component/input',
        // 正则校验规则
        regList: [
          //   {
          //   pattern: '/^1(3|4|5|7|8|9)\\d{9}$/',
          //   message: '手机号格式错误'
          // }
        ]
      },
      // 组件的插槽属性
      __slot__: {
        prepend: '',
        append: ''
      },
      __vModel__: 'mobile',
      placeholder: '请输入手机号',
      style: { width: '100%' },
      clearable: true,
      'prefix-icon': 'el-icon-mobile',
      'suffix-icon': '',
      // maxlength: 11,
      'show-word-limit': true,
      readonly: false,
      disabled: false
    }
  ]
  ,
  activeData: drawingDefalut[0],
  activeId: drawingDefalut[0].formId,
  idGlobal,
  index: 0
})

const { leftComponents, selfForm, drawingList, activeData, activeId } = toRefs(data)
watch(drawingList, (newVal, oldVal) => {
  // console.log({newVal})
  saveDrawingList(newVal)
  if (newVal.length === 0) {
    data.idGlobal = 100
  }
}, { deep: true })
onMounted(() => {
  if (props.editData.content) {
    // console.log("initData",props.editData)
    let { id, name, info, content } = props.editData
    data.selfForm.name = name
    data.selfForm.id = id
    data.selfForm.info = info
    content = JSON.parse(content)
    data.drawingList = content.fields
    const _content = JSON.parse(JSON.stringify(content))
    // delete _content.fields
    data.formConf = _content
  }
  activeFormItem(data.drawingList[0])
})
// 拖过去后的数据格式
const cloneComponent = function (origin) {
  const clone = JSON.parse(JSON.stringify(origin))
  const config = clone.__config__
  config.formId = ++data.idGlobal
  config.span = data.formConf.span
  config.renderKey = +new Date() // 改变renderKey后可以实现强制更新组件
  if (config.layout === 'colFormItem') {
    clone.__vModel__ = `field${data.idGlobal}`
    clone.placeholder !== undefined && (clone.placeholder += config.label)
  } else if (config.layout === 'rowFormItem') {
    config.componentName = `row${data.idGlobal}`
    config.gutter = this.formConf.gutter
  }
  tempActiveData = clone
  // tempActiveData.__config__.defaultValue = '12'
  return tempActiveData
}
// 右边要显示的数据
const activeFormItem = function (element) {
  //将选中的值的index赋值给子组件
  data.drawingList.forEach((item, index) => {
    if (item.__vModel__ == element.__vModel__) {
      data.index = index
    }
  });
  data.activeData = element
  data.activeId = element.__config__.formId
}
// 拖过去后右边自动显示当前的这个
const onEnd = function (obj) {
  if (obj.from !== obj.to) {
    data.activeData = tempActiveData
    data.activeId = data.idGlobal
  }
}
// 点击自动拖过去
const addComponent = function (item) {
  // console.log("dfdfdfdf")
  console.log(item);
  const clone = cloneComponent(item)
  data.drawingList.push(clone)
  data.activeFormItem(clone)
}
// 保存表单
const handlerSaveJSON = async function (formEl) {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      const formConfig = getFormConfSelf()
      // console.log({formConfig})
      //修改标签宽度，这data的formconf不知道为啥没defaultvalue 不然直接用data的formconf了
      formConfig.labelWidth = data.formConf.labelWidth
      // console.log(formConfig);
      // return 
      data.selfForm.content = JSON.stringify(formConfig)
      //  return false
      emit('getFormConfigDataResult', data.selfForm)
    } else {
      console.log('error submit!', fields)
    }
  })
}
const drawingItemDelete = function (index, parent) {
  parent.splice(index, 1)
  nextTick(() => {
    const len = data.drawingList.length
    if (len) {
      activeFormItem(data.drawingList[len - 1])
    }
  })
}
</script>


<style lang="scss" scoped>
@import '../styles/home';
</style>