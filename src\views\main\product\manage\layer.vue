<template>
  <Layer :layer="layer" @confirm="submit(formRef)" ref="layerDom">
    <Table ref="table" v-loading="loading" :data="form.unitNames" :show-page="false">
      <el-table-column label="单位" align="center" >
        <template #default="scope">
          {{form.unitNames[scope.$index]}}
        </template>
      </el-table-column>
      <el-table-column label="剩余库存" align="center" >
        <template #default="scope">
          {{form.unitNums[scope.$index]}}
        </template>
      </el-table-column>
      <el-table-column :label="`${form.type === 1 ? '入库' : '出库'}`" align="center" >
        <template #default="scope">
          <el-input-number v-model="form.nums[scope.$index]" :min="0" controls-position="right" />
        </template>
      </el-table-column>
    </Table>
  </Layer>
</template>

<script lang="ts" setup>
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { Minus } from '@element-plus/icons-vue'
import { ref, defineProps, defineEmits, reactive } from 'vue'
import Layer from '@/components/layer/index.vue'
import { ElMessage, ElLoading } from 'element-plus'
import {unitGroupSaveParams} from "@/type/units.type";
import Table from "@/components/table/index.vue";
import {changeStock} from "@/api/product";
const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})
const emit = defineEmits(['getTableData'])
const formRef = ref<FormInstance>()
const layerDom: Ref<LayerType | null> = ref(null)
const form = ref({
  unitNames: [],
  unitNums: [],
  type: 1,
  nums: [],
  id: 0,
  productId: 0
})

let loading: any = null;
const submit = async () => {
  try {
    loading = ElLoading.service({
      lock: true,
      text: 'Loading',
      background: 'rgba(0, 0, 0, 0.7)',
    })
    await changeStockFun();
  } catch (fieldsError) {
    console.log('error submit!', fieldsError);
  }
};

const changeStockFun = async () => {
  try {
    await changeStock({
      type: form.value.type,
      nums: form.value.nums,
      skuId: form.value.id,
      productId: form.value.productId
    });
    ElMessage({
      type: 'success',
      message: `${form.value.type === 1 ? '入库' : '出库'}`
    });
    emit('getTableData', false);
    layerDom.value && layerDom.value.close();
    loading.close()
  } catch (error) {
    // Handle error
  }
};

function init() { // 用于判断新增还是编辑功能
  if (props.layer.row) {
    form.value = JSON.parse(JSON.stringify({
      ...props.layer.row,
      type: props.layer.type
    }));
    form.value.nums = []
    form.value.unitNums.forEach(() => {
      (form.value.nums as number[]).push(0)
    })
  } else {
  }
}
init()

</script>

<style lang="scss" scoped>
:deep(.my-input .el-input__inner) {
  height: 40px;
}
</style>