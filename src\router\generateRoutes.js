const fs = require('fs');
const path = require('path');

// 视图文件夹的路径
const viewsPath = path.resolve(__dirname, '../views/main');
// 输出文件的路径
const outputPath = path.resolve(__dirname, './allRoutes.ts');

// 将路径转换为驼峰命名
function toCamelCase(str) {
    return str.replace(/[-_](\w)/g, (_, c) => (c ? c.toUpperCase() : ''));
}

// 生成import语句
function generateImportStatement(relativePath) {
    return `createNameComponent(() => import('${relativePath}'))`;
}

// 生成路由对象
function generateRoutes(dirPath, basePath = '') {
    const files = fs.readdirSync(dirPath);
    const routes = {};
    
    files.forEach((file) => {
        const filePath = path.join(dirPath, file);
        const stats = fs.statSync(filePath);
        if (stats.isDirectory()) {
            const dirName = toCamelCase(file);
            routes[dirName] = generateRoutes(filePath, `${basePath}/${file}`);
        } else if (file.endsWith('.vue')) {
            const baseName = toCamelCase(file.replace('.vue', ''));
            const relativePath = `@/views/main${basePath}/${file}`;
            routes[baseName] = generateImportStatement(relativePath);
        }
    });
    
    return routes;
}

// 将对象转换为字符串
function objectToString(obj, indent = '') {
    const entries = Object.entries(obj);
    const result = entries.map(([key, value]) => {
        const isObject = typeof value === 'object';
        return `${indent}${key}: ${isObject ? objectToString(value, `${indent}    `) : value},`;
    });
    
    return `{\n${result.join('\n')}\n${indent.slice(4)}}`;
}

// 主函数，生成并写入路由配置
function main() {
    const routes = generateRoutes(viewsPath);
    const routesStr = objectToString(routes);
    const content = `import { createNameComponent } from "./createNode";\n\nconst allRoutes = ${routesStr}\n\nexport default allRoutes;\n`;
    
    fs.writeFileSync(outputPath, content);
}

main();