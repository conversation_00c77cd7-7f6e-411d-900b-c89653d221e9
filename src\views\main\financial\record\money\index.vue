<template>
  <div class="layout-container">
    <div style="padding: 15px">
      <el-form label-position="right" label-width="100px">
        <el-form-item label="时间选择">
          <timepicker v-model="query.dateLimit" @change="getTableData(true)" />
        </el-form-item>
        <Unfold>
          <el-row>
            <el-form-item label="明细类型">
              <el-select
                v-model="query.channel"
                size="small"
                clearable
                placeholder="请选择"
                @change="getTableData(true)"
              >
                <el-option value="" label="全部"></el-option>
                <el-option
                  v-for="(item, index) in channelList"
                  :key="index"
                  :label="item"
                  :value="index"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="">
              <el-radio-group v-model="query.type" @change="getTableData(true)">
                <el-radio-button label="">全部</el-radio-button>
                <el-radio-button label="1">增加</el-radio-button>
                <el-radio-button label="2">减少</el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-row>

          <el-row>
            <el-form-item label="账号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.account"
                placeholder="请输入账号"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.phone"
                placeholder="请输入手机号"
              ></el-input>
            </el-form-item>
            <el-form-item label="昵称">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.nickname"
                placeholder="请输入昵称"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-button
              type="primary"
              :icon="Search"
              class="search-btn"
              @click="getTableData(true)"
            >
              {{ $t('message.common.search') }}
            </el-button>
          </el-row>
        </Unfold>
      </el-form>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column prop="id" label="ID" align="center" width="80" />
        <el-table-column prop="uid" label="用户id" align="center">
          <template #default="scope">
            <div><RightMenu :uid="scope.row.uid"></RightMenu></div>
          </template>
        </el-table-column>
        <el-table-column prop="account" label="账号" align="center" />
        <el-table-column prop="phone" label="手机号" align="center" />
        <el-table-column prop="nickName" label="昵称" align="center" />
        <el-table-column prop="money" label="金额" align="center" />
        <el-table-column prop="title" label="标题" align="center" />
        <el-table-column prop="type" label="金额类型" align="center">
          <template #default="scope">
            <div>
              <span v-if="scope.row.type == 1">增加</span>
              <span v-if="scope.row.type == 2">减少</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="明细类型" align="center">
          <template #default="scope">
            <div>
              <span>{{ channelList[scope.row.channel] }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" align="center" />
        <el-table-column prop="createTime" label="创建时间" align="center" />
      </Table>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import { moneyRecordList, moneyChannelList } from '@/api/financial'
  import Table from '@/components/table/index.vue'
  import { Search } from '@element-plus/icons-vue'
  import RightMenu from '@/components/rightMenu/index.vue'
  import Unfold from '@/components/unfold/index.vue'
  import timepicker from '@/components/timePicker/index.vue'
  // 存储搜索用的数据
  const query = reactive({
    account: '',
    phone: '',
    nickname: '',
    dateLimit: '',
    type: 0,
    channel: 0,
    keyword: '',
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref([])
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    moneyRecordList(params)
      .then(res => {
        let data: any = res.list
        data.forEach((d: any) => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  const channelList = ref()
  const getmoneyChannelList = () => {
    moneyChannelList().then(res => {
      channelList.value = res
      getTableData(true)
    })
  }
  getmoneyChannelList()
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }
</style>
