const fs = require('fs');
const path = require('path');
const Client = require('ssh2-sftp-client');

/**
 * 部署脚本
 */

class Deployer {
	constructor() {
		this.sftp = new Client();
		this.config = null;
		this.serverKey = null;
	}

	/**
	 * 解析命令行参数
	 */
	parseArgs() {
		const args = process.argv.slice(2);

		// 查找 --ip 参数的索引
		const ipIndex = args.findIndex(arg => arg === '--ip');

		if (ipIndex !== -1 && ipIndex + 1 < args.length) {
			this.serverKey = args[ipIndex + 1];
			console.log(`🚀 开始部署到服务器: ${this.serverKey}`);
		} else {
			process.exit(1);
		}
	}

	/**
	 * 加载配置文件
	 */
	loadConfig() {
		const configPath = path.join(process.cwd(), 'proxyConfig.json');

		if (!fs.existsSync(configPath)) {
			console.error('❌ 错误: 找不到 proxyConfig.json 配置文件');
			process.exit(1);
		}

		try {
			const configData = fs.readFileSync(configPath, 'utf8');
			const allConfigs = JSON.parse(configData);

			if (!allConfigs[this.serverKey]) {
				console.error(`❌ 错误: 在 proxyConfig.json 中找不到 "${this.serverKey}" 的配置`);
				console.log('可用的配置:', Object.keys(allConfigs).join(', '));
				process.exit(1);
			}

			this.config = {
				host: allConfigs[this.serverKey].ip,
				username: allConfigs[this.serverKey].user,
				password: allConfigs[this.serverKey].password,
				port: 22,
			};

			console.log(`📋 配置加载成功:`);
			console.log(`   服务器: ${this.config.host}`);
			console.log(`   用户: ${this.config.username}`);
		} catch (error) {
			console.error('❌ 错误: 解析配置文件失败', error.message);
			process.exit(1);
		}
	}

	/**
	 * 检查dist目录是否存在
	 */
	checkDistDirectory() {
		const distPath = path.join(process.cwd(), 'dist');

		if (!fs.existsSync(distPath)) {
			console.error('❌ 错误: dist 目录不存在，请先运行 yarn build 构建项目');
			process.exit(1);
		}

		console.log('✅ dist 目录检查通过');
	}

	/**
	 * 连接SSH服务器
	 */
	async connectSSH() {
		try {
			console.log('🔗 正在连接SSH服务器...');
			await this.sftp.connect(this.config);
			console.log('✅ SSH连接成功');
		} catch (error) {
			console.error('❌ SSH连接失败:', error.message);
			throw error;
		}
	}

	/**
	 * 检查并删除远程目录中的现有dist文件
	 */
	async cleanRemoteDirectory() {
		const remotePath = '/home/<USER>/dist';

		try {
			console.log('🧹 检查远程目录...');
			const exists = await this.sftp.exists(remotePath);

			if (exists) {
				console.log('🗑️  删除现有的 dist 目录...');
				await this.sftp.rmdir(remotePath, true);
				console.log('✅ 现有 dist 目录已删除');
			}

			// 确保 /home/<USER>
			const parentDir = '/home/<USER>';
			const parentExists = await this.sftp.exists(parentDir);
			if (!parentExists) {
				await this.sftp.mkdir(parentDir, true);
				console.log('✅ 创建了 /home/<USER>');
			}
		} catch (error) {
			console.error('❌ 清理远程目录失败:', error.message);
			throw error;
		}
	}

	/**
	 * 递归获取目录中的所有文件
	 */
	getAllFiles(dirPath, arrayOfFiles = []) {
		const files = fs.readdirSync(dirPath);

		files.forEach(file => {
			const fullPath = path.join(dirPath, file);
			if (fs.statSync(fullPath).isDirectory()) {
				arrayOfFiles = this.getAllFiles(fullPath, arrayOfFiles);
			} else {
				arrayOfFiles.push(fullPath);
			}
		});

		return arrayOfFiles;
	}

	/**
	 * 上传dist目录
	 */
	async uploadDistDirectory() {
		const localDistPath = path.join(process.cwd(), 'dist');
		const remoteDistPath = '/home/<USER>/dist';

		try {
			console.log('📤 开始上传 dist 目录...');

			// 获取所有需要上传的文件
			const allFiles = this.getAllFiles(localDistPath);
			console.log(`📁 发现 ${allFiles.length} 个文件需要上传`);

			let successful = 0;
			let failed = 0;

			const result = await this.sftp.uploadDir(localDistPath, remoteDistPath, {
				recursive: true,
				concurrency: 5, // 限制并发数，避免服务器压力过大
				tick: (localPath, remotePath, error) => {
					if (error) {
						failed++;
						console.log(`❌ 上传失败: ${path.relative(localDistPath, localPath)} - ${error.message}`);
					} else {
						successful++;
						console.log(`✅ 上传成功: ${path.relative(localDistPath, localPath)} (${successful}/${allFiles.length})`);
					}
				},
			});

			if (result) {
				console.log('🎉 dist 目录上传完成!');

				// 如果tick回调没有被调用，使用文件总数作为成功数量
				if (successful === 0 && failed === 0) {
					successful = allFiles.length;
					console.log(`📊 统计: 成功 ${successful} 个文件 (基于文件总数), 失败 ${failed} 个文件`);
					console.log('ℹ️  注意: 由于服务器兼容性问题，无法获取详细的上传进度，但所有文件已成功上传');
				} else {
					console.log(`📊 统计: 成功 ${successful} 个文件, 失败 ${failed} 个文件`);
				}
			} else {
				throw new Error('上传过程中出现错误');
			}
		} catch (error) {
			console.error('❌ 上传 dist 目录失败:', error.message);
			throw error;
		}
	}

	/**
	 * 关闭SSH连接
	 */
	async closeConnection() {
		try {
			await this.sftp.end();
			console.log('🔐 SSH连接已关闭');
		} catch (error) {
			console.error('⚠️  关闭SSH连接时出现警告:', error.message);
		}
	}

	/**
	 * 主部署流程
	 */
	async deploy() {
		try {
			this.parseArgs();
			this.loadConfig();
			this.checkDistDirectory();

			await this.connectSSH();
			await this.cleanRemoteDirectory();
			await this.uploadDistDirectory();

			console.log('🎊 部署完成! 🎊');
			console.log(`🌐 您的应用已部署到: ${this.config.host}`);
		} catch (error) {
			console.error('💥 部署失败:', error.message);
			process.exit(1);
		} finally {
			await this.closeConnection();
		}
	}
}

// 运行部署
const deployer = new Deployer();
deployer.deploy();
