import {login<PERSON>pi, getInfo<PERSON>pi, loginOut<PERSON>pi, getImageUrl} from '@/api/user'
import { ActionContext } from 'vuex'
import type {LoginApiParams} from '@/type/user.type'
export interface userState {
  token: string,
  info: object,
  imgUrl: string
}
const state = (): userState => ({
  token: '', // 登录token
  info: {},  // 用户信息
  imgUrl: ''
})

// getters
const getters = {
  token(state: userState) {
    return state.token
  }
}

// mutations
const mutations = {
  tokenChange(state: userState, token: string) {
    state.token = token
  },
  infoChange(state: userState, info: object) {
    state.info = info
  },
  imgUrlChange(state: userState, imgUrl: string) {
    state.imgUrl = imgUrl
  }
}

// actions
const actions = {
  // login by login.vue
  login({ commit, dispatch }: ActionContext<userState, userState>, params: LoginApiParams) {
    return new Promise((resolve, reject) => {
      loginApi(params)
        .then(res => {
          commit('tokenChange', res.token)
          dispatch('getInfo', { token: res.token })
            .then(infoRes => {
              resolve(res.token)
            })
        }).catch(err => {
          reject(err)
        })
    })
  },
  getImgUrl({ commit }: ActionContext<userState, userState>) {
    return new Promise((resolve, reject) => {
      getImageUrl()
          .then(res => {
            commit('imgUrlChange', res)
            resolve(res)
          }).catch(err => {
        reject(err)
      })
    })
  },
  // get user info after user logined
  getInfo({ commit }: ActionContext<userState, userState>) {
    return new Promise((resolve, reject) => {
      getInfoApi()
        .then(res => {
      
          commit('infoChange', res)
          resolve(res)
        })
    })
  },

  // login out the system after user click the loginOut button
  loginOut({ commit }: ActionContext<userState, userState>) {
    loginOutApi()
      .then(res => {

      })
      .catch(error => {

      })
      .finally(() => {
        localStorage.removeItem('tabs')
        localStorage.removeItem('vuex')
        sessionStorage.removeItem('vuex')
        location.reload()
      })
  }
}

export default {
  namespaced: true,
  state,
  actions,
  getters,
  mutations
}
