import request from "@/utils/system/request";
import type {unitGroupSaveParams} from "@/type/units.type";
import {UnitListParams} from "@/type/units.type";
import {ResponseList} from "@/type/index.type";

/**
 * 单位组新增
 * @param data
 */
export function unitGroupSave (data: unitGroupSaveParams) {
    return request<any>({
        url: '/api/admin/product/unitGroup/save',
        method: 'POST',
        data: data
    })
}

//单位组列表
export function getUnitGroupList(data: UnitListParams):Promise<ResponseList<any[]>> {
    return request({
        url: "/api/admin/product/unitGroup/list",
        method: "get",
        params: data,
    })
}

/**
 * 单位组编辑
 * @param data
 */
export function unitGroupUpdate (data: unitGroupSaveParams) {
    return request<any>({
        url: '/api/admin/product/unitGroup/update',
        method: 'POST',
        data: data
    })
}

/**
 * 单位组删除
 * @param ids
 */
export function unitGroupDelete (ids: string) {
    return request<any>({
        url: `/api/admin/product/unitGroup/delete/${ids}`,
        method: 'GET',
    })
}



/**
 *  获取自己管理的区域的列表
 * @param data
 */
export function selfAreaList () {
    return request<any>({
        url: '/api/area/selfAreaList',
        method: 'GET',
    })
}

/**
 * 切换当前管理区域
 * @param id
 */
export function changeCurrentArea (id: number) {
    return request<any>({
        url: `/api/area/changeCurrentArea/${id}`,
        method: 'GET',
    })
}

/**
 * 获取所有管理区域
 */
export function allAreaList () {
    return request<any>({
        url: `/api/area/allAreaList`,
        method: 'GET',
    })
}