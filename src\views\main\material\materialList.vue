<template>
  <div class="layout-container" style="padding: 15px">
    <div style="text-align: left">
      <el-form label-width="100px" :inline="false">
        <el-form-item label="库存筛选">
          <el-radio-group v-model="stockFilter" @change="handleStockFilterChange">
            <el-radio-button
              v-for="item in stockFilterOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label-width="0">
          <el-form-item label="原料分类">
            <el-cascader
              :options="categoryOptions"
              :props="categoryProps"
              clearable
              v-model="query.cateId"
              @change="getTableData(true)"
              placeholder="请选择原料分类"
            />
          </el-form-item>
          <el-form-item label="原料搜索">
            <div class="layout-container-form-search">
              <el-input
                v-model="query.keywords"
                placeholder="请输入关键字"
                clearable
                @clear="getTableData(true)"
              ></el-input>
              <el-button
                type="primary"
                :icon="Search"
                class="search-btn"
                @click="getTableData(true)"
              >
                搜索
              </el-button>
            </div>
          </el-form-item>
        </el-form-item>
      </el-form>
    </div>
    <div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          新增原料
        </el-button>
      </div>
    </div>

    <div class="table-tip">
      <el-text type="info" size="small">
        <el-icon><InfoFilled /></el-icon>
        提示：在表格行上右键点击可快速进行操作
      </el-text>
    </div>
    <!-- 表格 -->
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        :showContextMenu="true"
        :contextMenuItems="contextMenuItems"
        @getTableData="getTableData"
        @context-menu-click="handleContextMenuClick"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="ID" align="center" width="80" />
        <el-table-column
          prop="rawMaterialName"
          label="原料名称"
          align="center"
        />
        <el-table-column label="分类" align="center">
          <template #default="scope">
            {{ getCategoryName(scope.row.cateId) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="unitGroupStr"
          label="库存"
          align="center"
        />
        <el-table-column
          label="警戒库存"
          align="center"
          width="120"
        >
          <template #default="scope">
            {{ formatStoreStock(scope.row) }}
          </template>
        </el-table-column>
				<el-table-column
          prop="rawMaterialNum"
          label="数量"
          align="center"
					sortable="custom"
          sort-by="stock"
        />
        <el-table-column label="操作" align="center" width="250">
          <template #default="scope">
            <el-button
              type="primary"
              size="small"
              @click="handleStock(scope.row)"
            >
              库存管理
            </el-button>
            <el-button type="info" size="small" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(scope.row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </Table>
    </div>

    <!-- 出入库弹窗 -->
    <MaterialStockDialog
      v-model:visible="stockDialogVisible"
      :material-data="currentMaterial"
      @confirm="handleStockConfirm"
      @cancel="handleStockCancel"
    />

    <!-- 新增/编辑弹窗 -->
    <MaterialFormDialog
      v-model:visible="formDialogVisible"
      :material-data="editMaterialData"
      :category-options="categoryOptions"
      @confirm="handleFormConfirm"
      @cancel="handleFormCancel"
    />
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { useRoute } from 'vue-router'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import {
    Search,
    Plus,
    Edit,
    Delete,
    Setting,
    InfoFilled,
  } from '@element-plus/icons-vue'
  import { Page } from '@/components/table/type'
  import Table from '@/components/table/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import MaterialStockDialog from './components/MaterialStockDialog.vue'
  import MaterialFormDialog from './components/MaterialFormDialog.vue'
  import {
    getMaterialList,
    deleteMaterial,
    changeMaterialStock,
    saveMaterial,
    updateMaterial,
  } from '@/api/material'
  import { treeCategroy } from '@/api/categoryApi'
  import type {
    MaterialListParams,
    MaterialListRes,
  } from '@/type/material.type'
  import type { TreeCategroyRes } from '@/type/category.type'

  const route = useRoute()

  // 库存筛选状态
  const stockFilter = ref('')

  // 库存筛选选项
  const stockFilterOptions = [
    { label: '全部', value: '' },
    { label: '警戒', value: 'stockLack' }
  ]

  // 搜索条件
  const query = reactive<MaterialListParams>({
    cateId: '',
    keywords: '',
    page: 1,
    limit: 20,
  })

  // 库存排序状态
  const currentSort = ref<{ prop: string; order: string } | null>(null)

  // 分页参数
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })

  // 表格数据
  const loading = ref(true)
  const tableData = ref<MaterialListRes[]>([])

  // 分类选择器配置
  const categoryOptions = ref<TreeCategroyRes[]>([])
  const categoryProps = {
    value: 'id',
    label: 'name',
    children: 'child',
    emitPath: false,
  }

  // 出入库弹窗相关
  const stockDialogVisible = ref(false)
  const currentMaterial = ref<MaterialListRes>({
    id: 0,
    cateId: '',
    rawMaterialName: '',
    image: '',
    rawMaterialNum: 0,
    unit: '',
  })

  // 新增/编辑弹窗相关
  const formDialogVisible = ref(false)
  const editMaterialData = ref<MaterialListRes | null>(null)

  // 右键菜单配置
  const contextMenuItems = ref([
    {
      key: 'stock',
      label: '库存管理',
      icon: Setting,
    },
    {
      key: 'edit',
      label: '编辑',
      icon: Edit,
    },
    {
      key: 'delete',
      label: '删除',
      icon: Delete,
      type: 'delete',
    },
  ])

  // 处理表格排序变化
  const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
    currentSort.value = order ? { prop, order } : null
    getTableData(true)
  }

  // 获取表格数据
  const getTableData = (init?: boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }

    const params: MaterialListParams = {
      ...query,
      page: page.index,
      limit: page.size,
    }

    // 添加库存筛选参数
    if (stockFilter.value) {
      params.extra = stockFilter.value
    }

    // 添加库存排序参数
    if (currentSort.value && currentSort.value.prop === 'rawMaterialNum') {
      params.sortColumn = 'stock'
      params.sortType = currentSort.value.order === 'ascending' ? 'asc' : 'desc'
    }

    getMaterialList(params)
      .then(res => {
        const data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
        ElMessage.error('获取原料列表失败')
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 获取分类数据
  const getCategoryData = () => {
    // type: 9 表示原料分类
    treeCategroy({ type: 9, status: -1 }).then(res => {
      categoryOptions.value = res
    })
  }

  // 根据分类ID获取分类名称
  const getCategoryName = (cateId: string) => {
    const findCategory = (
      categories: TreeCategroyRes[],
      id: string
    ): string => {
      for (const category of categories) {
        if (category.id?.toString() === id) {
          return category.name || ''
        }
        if (category.child && category.child.length > 0) {
          const found = findCategory(category.child, id)
          if (found) return found
        }
      }
      return ''
    }
    return findCategory(categoryOptions.value, cateId)
  }

  // 格式化警戒库存显示
  const formatStoreStock = (row: MaterialListRes) => {
    // 如果有多单位数据
    if (row.storeStockNums && row.unitNames && row.storeStockNums.length > 0 && row.unitNames.length > 0) {
      const stockParts: string[] = []
      for (let i = 0; i < Math.min(row.storeStockNums.length, row.unitNames.length); i++) {
        if (row.storeStockNums[i] > 0) {
          stockParts.push(`${row.storeStockNums[i]}${row.unitNames[i]}`)
        }
      }
      return stockParts.length > 0 ? stockParts.join(' ') : '-'
    }
    // 如果没有多单位数据，返回默认显示
    return '-'
  }

  // 库存筛选处理函数
  const handleStockFilterChange = () => {
    getTableData(true)
  }

  // 库存管理操作
  const handleStock = (row: MaterialListRes) => {
    currentMaterial.value = { ...row }
    stockDialogVisible.value = true
  }

  // 新增操作
  const handleAdd = () => {
    editMaterialData.value = null
    formDialogVisible.value = true
  }

  // 编辑操作
  const handleEdit = (row: MaterialListRes) => {
    editMaterialData.value = { ...row }
    formDialogVisible.value = true
  }

  // 删除操作
  const handleDelete = (row: MaterialListRes) => {
    ElMessageBox.confirm(
      `确定要删除原料"${row.rawMaterialName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      deleteMaterial(row.id)
        .then(() => {
          ElMessage.success('删除成功')
          getTableData(tableData.value.length === 1)
        })
        .catch(() => {
          ElMessage.error('删除失败')
        })
    })
  }

  // 出入库弹窗确认
  const handleStockConfirm = async (data: any) => {
    try {
      await changeMaterialStock(data)
      ElMessage.success(`${data.type === 1 ? '入库' : '出库'}成功`)
      stockDialogVisible.value = false
      getTableData(false) // 刷新列表
    } catch (error) {
      console.log(error)
    }
  }

  // 出入库弹窗取消
  const handleStockCancel = () => {
    stockDialogVisible.value = false
  }

  // 新增/编辑弹窗确认
  const handleFormConfirm = async (data: any) => {
    try {
      if (editMaterialData.value && editMaterialData.value.id > 0) {
        // 编辑
        await updateMaterial(data)
        ElMessage.success('更新成功')
      } else {
        // 新增
        await saveMaterial(data)
        ElMessage.success('添加成功')
      }
      formDialogVisible.value = false
      getTableData(false) // 刷新列表
    } catch (error) {
      console.log('error', error)
    }
  }

  // 新增/编辑弹窗取消
  const handleFormCancel = () => {
    formDialogVisible.value = false
  }

  // 处理右键菜单点击
  const handleContextMenuClick = ({
    item,
    row,
  }: {
    item: any
    row: MaterialListRes
  }) => {
    switch (item.key) {
      case 'stock':
        handleStock(row)
        break
      case 'edit':
        handleEdit(row)
        break
      case 'delete':
        handleDelete(row)
        break
    }
  }

  // 初始化
  onMounted(() => {
    // 从路由参数中读取extra值
    const extraParam = route.query.extra as string
    if (extraParam) {
      stockFilter.value = extraParam
    }

    getCategoryData()
    getTableData(true)
  })
</script>

<style lang="scss" scoped>
  .layout-container-form-search {
    display: flex;
    gap: 10px;
    align-items: center;
  }

  .search-btn {
    white-space: nowrap;
  }

  .table-tip {
    margin-top: 12px;
    margin-left: 14px;
    padding: 8px 12px;
    background-color: #f0f9ff;
    border: 1px solid #e0f2fe;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 6px;

    .el-text {
      display: flex;
      align-items: center;
      gap: 6px;
      color: #0369a1;
    }
  }
</style>
