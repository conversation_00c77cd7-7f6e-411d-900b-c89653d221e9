<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="85%"
    center
    align-center
    destroy-on-close
    custom-class="centered-dialog"
  >
    <el-descriptions
        direction="vertical"
        :column="3"
        border
    >
      <el-descriptions-item label="订单ID">{{form.id}}</el-descriptions-item>
      <el-descriptions-item label="操作类型">
        <el-tag :type="form.type === 1 ? 'warning' : 'success'">
          {{ form.type === 1 ? '出库' : '入库' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="手机号">{{form.phone || '暂无'}}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{form.createTime}}</el-descriptions-item>
      <el-descriptions-item label="创建人">{{form.createBy}}</el-descriptions-item>
      <el-descriptions-item label="订单号" :span="3">{{form.orderNo}}</el-descriptions-item>

      <!-- 新增的6个字段 -->
      <el-descriptions-item label="客户名称">{{form.customerName || '暂无'}}</el-descriptions-item>
      <el-descriptions-item label="审批">{{form.examineApprove || '暂无'}}</el-descriptions-item>
      <el-descriptions-item label="审核">{{form.examine || '暂无'}}</el-descriptions-item>
      <el-descriptions-item label="制单">{{form.documentPreparation || '暂无'}}</el-descriptions-item>
      <el-descriptions-item label="收货人">{{form.consignee || '暂无'}}</el-descriptions-item>
      <el-descriptions-item label="货车司机">{{form.driver || '暂无'}}</el-descriptions-item>

      <el-descriptions-item label="备注" :span="3">{{form.mark || '暂无'}}</el-descriptions-item>
      <el-descriptions-item label="库存变化详情" :span="3">
        <div class="table-container">
          <Table
              :data="form.stockLogResponseList"
              :showPage="false"
              class="descriptions-item"
          >
          <el-table-column prop="id" label="ID" align="center" width="60" />
          <el-table-column prop="productName" label="产品名称" align="center" min-width="120"/>
          <el-table-column prop="suk" label="产品规格" align="center" min-width="100"/>
          <el-table-column prop="areaId" label="仓库区域" align="center" width="80">
            <template #default="scope">
              <span>区域{{ scope.row.areaId }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="beforeStock" label="修改前库存" align="center" min-width="120">
            <template #default="scope">
              <span>{{ scope.row.beforeStock || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="changeStock" label="变化数量" align="center" min-width="100">
            <template #default="scope">
              <span
                :class="scope.row.type === 1 ? 'text-warning' : 'text-success'"
              >
                {{ scope.row.type === 1 ? '-' : '+' }}{{ scope.row.changeStock || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="afterStock" label="修改后库存" align="center" min-width="120">
            <template #default="scope">
              <span>{{ scope.row.afterStock || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="unitPrice" label="单价" align="center" width="80">
            <template #default="scope">
              <span>{{ formatPrice(scope.row.unitPrice) }}元</span>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="总价" align="center" width="80">
            <template #default="scope">
              <span>{{ formatPrice(scope.row.price) }}元</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="操作时间" align="center" min-width="140"/>
          <el-table-column prop="mark" label="单位信息" align="center" width="100"/>
        </Table>
        </div>
      </el-descriptions-item>

      <!-- 赠品变化详情表格 -->
      <el-descriptions-item label="赠品变化详情" :span="3">
        <div v-if="form.giftLogResponseList && form.giftLogResponseList.length > 0" class="table-container">
          <Table
              :data="form.giftLogResponseList"
              :showPage="false"
              class="descriptions-item"
          >
          <el-table-column prop="id" label="ID" align="center" width="60" />
          <el-table-column prop="productName" label="产品名称" align="center" min-width="120"/>
          <el-table-column prop="suk" label="产品规格" align="center" min-width="100"/>
          <el-table-column prop="areaId" label="仓库区域" align="center" width="80">
            <template #default="scope">
              <span>区域{{ scope.row.areaId }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="beforeStock" label="修改前库存" align="center" min-width="120">
            <template #default="scope">
              <span>{{ scope.row.beforeStock || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="changeStock" label="变化数量" align="center" min-width="100">
            <template #default="scope">
              <span
                :class="scope.row.type === 1 ? 'text-warning' : 'text-success'"
              >
                {{ scope.row.type === 1 ? '-' : '+' }}{{ scope.row.changeStock || '-' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="afterStock" label="修改后库存" align="center" min-width="120">
            <template #default="scope">
              <span>{{ scope.row.afterStock || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="unitPrice" label="单价" align="center" width="80">
            <template #default="scope">
              <span>{{ formatPrice(scope.row.unitPrice) }}元</span>
            </template>
          </el-table-column>
          <el-table-column prop="price" label="总价" align="center" width="80">
            <template #default="scope">
              <span>{{ formatPrice(scope.row.price) }}元</span>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="操作时间" align="center" min-width="140"/>
          <el-table-column prop="mark" label="单位信息" align="center" width="100"/>
        </Table>
        </div>
        <div v-else class="empty-gift-message">
          <span class="text-gray-400">暂无赠品记录</span>
        </div>
      </el-descriptions-item>
    </el-descriptions>
  </el-dialog>
</template>

<script setup lang="ts">
import {ref, computed, watch} from "vue"
import {purchaseOrderInfo} from "@/api/order";
import Table from "@/components/table/index.vue";

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  row: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:show'])

const form = ref<any>({})

// 计算弹窗显示状态
const dialogVisible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// 格式化价格显示的函数
const formatPrice = (price: number | string | undefined | null) => {
  if (price === null || price === undefined || price === '') {
    return '0.00'
  }
  const numPrice = typeof price === 'string' ? parseFloat(price) : price
  if (isNaN(numPrice)) {
    return '0.00'
  }
  return numPrice.toFixed(2)
}

async function init() { // 用于判断新增还是编辑功能
  if (props.row) {
    form.value = await purchaseOrderInfo(props.row.id)
  } else {
    // 重置表单数据
    form.value = {}
  }
}

// 优化后的watch逻辑：合并监听，避免重复调用
// 同时监听 show 和 row 的变化，只在弹窗显示且有数据时调用一次 init
watch(
  [() => props.show, () => props.row],
  ([newShow, newRow], [oldShow, oldRow]) => {
    // 只有在以下情况下才调用 init：
    // 1. 弹窗从隐藏变为显示，且有 row 数据
    // 2. 弹窗已显示，但 row 数据发生了变化（且新的 row 不为空）
    const shouldInit =
      (newShow && newRow && (!oldShow || newRow !== oldRow)) ||
      (newShow && newRow && !oldRow)

    if (shouldInit) {
      init()
    } else if (!newShow) {
      // 弹窗关闭时清理表单数据
      form.value = {}
    }
  },
  { immediate: true, deep: true }
)
</script>

<style lang="scss">
// 全局弹窗居中和高度限制样式（不使用 scoped）
.el-overlay-dialog {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 1vh 0 !important; // 减少上下边距，避免溢出
}

.centered-dialog {
  margin: 0 !important;
  max-height: calc(100vh - 4vh) !important; // 确保总高度不超过视口，留出4vh的安全边距
  display: flex !important;
  flex-direction: column !important;

  .el-dialog__header {
    flex-shrink: 0; // 头部不收缩
    padding: 15px 20px 10px 20px; // 减少头部内边距
  }

  .el-dialog__body {
    flex: 1 !important;
    overflow-y: auto !important; // 内容区域可滚动
    padding: 5px 20px 15px 20px; // 减少内容区域内边距
    max-height: calc(100vh - 4vh - 80px) !important; // 减去边距和头部高度
  }

  .el-dialog__footer {
    flex-shrink: 0; // 底部不收缩
    padding: 10px 20px 15px 20px;
  }
}

// 强制覆盖 Element Plus 默认样式
.el-dialog__wrapper {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
</style>

<style scoped lang="scss">
// 优化弹窗内容布局
:deep(.el-descriptions) {
  margin-bottom: 0; // 移除默认的底部边距
}

:deep(.el-descriptions__body) {
  .el-descriptions__table {
    margin-bottom: 0; // 移除表格底部边距
  }
}

.descriptions-item {
  min-height: 120px; // 减少最小高度以节省空间
}

.table-container {
	height: 220px; // 调整表格高度以适应弹窗内容
  overflow: hidden; // 防止容器本身出现滚动条

  :deep(.system-table-box) {
    height: 100%;

    .system-table {
      height: 100%;

      .el-table {
        height: 100% !important;
      }

      .el-table__body-wrapper {
        max-height: calc(100% - 40px); // 减去表头高度
        overflow-y: auto; // 确保垂直滚动
      }
    }
  }
}

// 操作类型颜色样式
.text-success {
  color: #67c23a;
  font-weight: 500;
}

.text-warning {
  color: #e6a23c;
  font-weight: 500;
}

// 空赠品消息样式
.empty-gift-message {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50px;
  background-color: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;

  .text-gray-400 {
    color: #999;
    font-size: 14px;
  }
}

// 表格样式优化
:deep(.el-table) {
  min-width: 1100px; // 设置表格最小宽度，确保所有列正常显示

  .el-table__header-wrapper {
    th {
      background-color: #f5f7fa;
      color: #606266;
      font-weight: 600;
    }
  }

  .el-table__body-wrapper {
    td {
      padding: 8px 0;
    }
  }
}



// 响应式处理 - 在小屏幕上调整弹窗宽度和高度
@media screen and (max-width: 1400px) {
  .centered-dialog {
    width: 95% !important;
    max-height: calc(100vh - 3vh) !important; // 确保不溢出，留出3vh安全边距

    .el-dialog__body {
      max-height: calc(100vh - 3vh - 80px) !important;
    }
  }
}

@media screen and (max-width: 768px) {
  .el-overlay-dialog {
    padding: 0.5vh 0 !important; // 在移动端最小化上下边距
  }

  .centered-dialog {
    width: 98% !important;
    max-height: calc(100vh - 1vh) !important; // 在移动端最大化使用屏幕空间，只留1vh安全边距

    .el-dialog__body {
      max-height: calc(100vh - 1vh - 60px) !important; // 减少头部占用空间
      padding: 5px 15px 10px 15px; // 进一步减少内边距
    }

    .el-dialog__header {
      padding: 8px 15px 5px 15px; // 进一步减少头部内边距
    }
  }

  .table-container {
    height: 180px; // 在移动端进一步减少表格高度

    // 在移动端显示滚动提示
    &::after {
      content: "← 左右滑动查看更多 →";
      display: block;
      text-align: center;
      color: #999;
      font-size: 12px;
      padding: 8px 0;
      background: #f9f9f9;
      border-top: 1px solid #eee;
    }
  }

  .descriptions-item {
    min-height: 100px; // 在移动端进一步减少最小高度
  }
}
</style>