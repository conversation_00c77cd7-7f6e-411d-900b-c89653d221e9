
/**
 * 单位组新增参数
 */
export interface unitGroupSaveParams {
    id?: number;
    groupName: string;
    base: string;
    unitNames: string;
    scales: string;
    mark: string;
    unitNameList?: string[]
    scaleList?: number[]
    type?: number; // 单位类型：1-产品单位，2-原料单位
}

/**
 * 单位组列表请求体
 */
export interface UnitListParams {
    keywords?: string
    page?: number
    limit?: number
    type?: number // 单位类型：1-产品单位，2-原料单位
}
