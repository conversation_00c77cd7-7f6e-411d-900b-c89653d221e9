/**
 * 
 * @param index  字段
 * @param fields  字段名称
 * @param list  字典列表
 * @returns 
 */
export function getDictionaryData<T>(index: number, fields: string, list: T[]): any {
    let obj = list.find(item => item[fields] === index)


    return obj
}

export interface AttrValueItem {
    [key: string]: any; // 允许任意字符串键和任意类型的值
    unitNums: number[]; // unitNums是一个数字数组
    attrValueNo?: string; // 产品编码
    otPrice?: number; // 原价
    price?: number; // 会员价
}

//多规格算法
export function generateBaseData(arr: any, unitListLength: number) {
    if (arr.length === 0) return [];
    const initUnitNums = Array(unitListLength).fill(0);
    
    if (arr.length === 1) {
        let [item_spec] = arr;
        return item_spec.detail.map((x: any) => {
            return {
                [item_spec.value]: x,
                unitNums: [...initUnitNums], // 添加unitNums属性
                attrValueNo: '', // 添加产品编码字段
                otPrice: 0, // 添加原价字段
                price: 0 // 添加会员价字段
            };
        });
    }
    
    if (arr.length >= 1) {
        return arr.reduce((accumulator: any, spec_item: any) => {
            let acc_value_list = Array.isArray(accumulator.detail) ? accumulator.detail : accumulator;
            let item_value_list = spec_item.detail;
            let result = [];
            for (let i in acc_value_list) {
                for (let j in item_value_list) {
                    let temp_data = {
                        unitNums: undefined,
                        attrValueNo: '',
                        otPrice: 0,
                        price: 0
                    };
                    if (typeof acc_value_list[i] === 'object' && acc_value_list[i] !== null) {
                        // accumulator是对象的情况
                        temp_data = {
                            ...acc_value_list[i],
                            [spec_item.value]: item_value_list[j]
                        };
                    } else {
                        // 如果是单个规格项
                        temp_data[accumulator.value] = acc_value_list[i];
                        temp_data[spec_item.value] = item_value_list[j];
                    }
                    // @ts-ignore
                    temp_data.unitNums = [...initUnitNums]; // 添加unitNums属性
                    temp_data.attrValueNo = ''; // 添加产品编码字段
                    if (!temp_data.otPrice) temp_data.otPrice = 0; // 添加原价字段
                    if (!temp_data.price) temp_data.price = 0; // 添加会员价字段
                    result.push(temp_data);
                }
            }
            return result;
        });
    }
}
