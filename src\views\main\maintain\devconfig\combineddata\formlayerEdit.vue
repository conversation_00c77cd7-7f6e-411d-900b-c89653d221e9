<template>
    <Layer :layer="layer" @confirm="submit(ruleForm)" ref="layerDom">
        <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" style="margin-right:30px;">
            <el-form-item label="排序" prop="sort">
                <el-input-number v-model="form.sort" :min="0" />
            </el-form-item>
            <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" style="margin-right:30px;">
                <el-form-item label="状态">
                    <el-switch v-model="form.status" class="ml-2"
                        style="--el-switch-on-color: #13ce66; --el-switch-off-color: #ff4949" />
                </el-form-item>
                <Parser :form-conf="formConf.content" @submit="handlerSubmit" v-if="formConf.render"
                    :is-edit="formConf.isEdit" :form-edit-data="currentEditData" />
            </el-form>
        </el-form>

    </Layer>
</template>
  
<script lang="ts" setup>
import Parser from '@/components/FormGenerator/components/parser/Parser.vue'
import { getFormConfigInfo, configInfo } from '@/api/systemFormConfig'
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import type { ElFormItemContext } from 'element-plus/lib/el-form/src/token'
import { defineEmits, ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import Layer from '@/components/layer/index.vue'
import { groupDataSave, groupDataUpate } from '@/api/maintain'
import type { FormInstance, FormRules } from 'element-plus'
import type { GroupDataParams, SystemFormItemCheckRequest } from '@/type/maintain.type'


const props = defineProps({
    layer: {
        type: Object,
        default: () => {
            return {
                show: false,
                title: '',
                showButton: true
            }
        }
    }
})

const emit = defineEmits(['getTableData'])
const vfdRef = ref(null)
const ruleForm: Ref<ElFormItemContext | null> = ref(null)
const layerDom: Ref<LayerType | null> = ref(null)
const form = ref({
    id: 0,
    sort: 0,
    status: true,
    gid: 0,
    formId: 0
})

const rules = reactive<FormRules>({
    sort: [{ required: true, message: '请输入排序', trigger: 'blur' }],

})
const formConf = reactive({
    content: { fields: [] }, id: null, render: false, isEdit: false
})
const getForm = (id: any) => {
    formConf.content = { fields: [] }
    formConf.render = false
    getFormConfigInfo({ id: id }).then(async res => {
        formConf.content = JSON.parse(res.content)
        formConf.isEdit = currentEditData.value !== null
        formConf.render = true
    })
}
const formId = ref(props.layer.row.formId)
const listDataId = ref(props.layer.row.id)
const currentEditData = ref('')

async function init() { // 用于判断新增还是编辑功能

    await getForm(formId.value)

    if (props.layer.row) {
        form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
        if ('status' in form.value === false) {
            form.value.status = true

        } if ('sort' in form.value === false) {
            form.value.sort = 0
        }
    } else {

    }
    if (props.layer.row.id != undefined) {
        currentEditData.value = props.layer.row

    }
}

init()

function handlerSubmit(data: any) {
    let obj: GroupDataParams = {
        gid: 0,
        form: {
            id: 0,
            sort: 0,
            status: true,
            fields: [{
                name: '',
                title: "",
                value: ''
            }]
        }

    }

    let fields: Array<SystemFormItemCheckRequest> = []
    for (const key in data) {
        let fieldsobj: SystemFormItemCheckRequest = {
            name: key,
            title: key,
            value: data[key]
        }
        fields.push(fieldsobj)
    }
    obj.gid = form.value.gid;
    obj.form!.fields = fields
    obj.form!.id = form.value.formId
    obj.form!.sort = form.value.sort
    obj.form!.status = form.value.status
    submit(obj)
}

function submit(obj: GroupDataParams) {
    if (ruleForm.value) {
        ruleForm.value.validate((valid: any) => {
            if (valid) {
                let params = obj
                if (listDataId.value != undefined) {
                    updateForm(params)
                } else {
                    addForm(params)
                }
            } else {
                return false;
            }
        });
    }
}
// 新增提交事件
function addForm(params: GroupDataParams) {
    groupDataSave(params)
        .then(res => {
            ElMessage({
                type: 'success',
                message: '新增成功'
            })
            emit('getTableData', true)
            layerDom.value && layerDom.value.close()
        })
}
// 编辑提交事件
function updateForm(params: GroupDataParams) {
    params.listDataId = listDataId.value


    groupDataUpate(params)
        .then(res => {
            ElMessage({
                type: 'success',
                message: '编辑成功'
            })
            emit('getTableData', false)
            layerDom.value && layerDom.value.close()
        })
}



</script>
  
<style lang="scss" scoped></style>