<template>
  <Layer :layer="layer" @confirm="submit(formRef)" ref="layerDom">
    <el-form :model="form" :rules="rules" ref="formRef" label-width="120px">
      <el-form-item label="多单位比例" style="display: flex; align-items: center;">
        <text v-for="unit in units.slice().reverse()">
          {{unit.name}}：
        </text>
        <text>{{form.base}}</text>
        <div v-if="form.base">&nbsp;&nbsp;&nbsp;(
          <text v-for="unit in units.slice().reverse()">
            {{unit.value}}:
          </text>
          <text>1</text>
          )</div>
      </el-form-item>
      <el-form-item label="基本组名称" prop="groupName" required style="display: flex; align-items: center;">
        <el-input class="my-input" style="width: 180px;" v-model="form.groupName" />
      </el-form-item>
      <el-form-item label="基本单位" prop="base" required style="display: flex; align-items: center;">
        <el-input class="my-input" style="width: 180px;" v-model="form.base" />
      </el-form-item>
      <el-form-item
          v-for="(unit, index) in units"
          :key="index"
          :label="'单位 ' + (index + 1)"
      >
        <el-row :gutter="10">
          <el-col :span="8">
            <el-input v-model="unit.name" placeholder="单位名称" />
          </el-col>
          <el-col :span="2">
            <text>==</text>
          </el-col>
          <el-col :span="8">
            <el-input-number v-model="unit.value" placeholder="等于多少" />
          </el-col>
          <el-col :span="2">
            {{form.base}}
          </el-col>
          <el-col :span="4">
            <el-button type="danger" :icon="Minus" @click="removeUnit(index)">移除</el-button>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="addUnit(formRef)">添加单位</el-button>
      </el-form-item>
    </el-form>
  </Layer>
</template>

<script lang="ts" setup>
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import type { FormInstance } from 'element-plus'
import { Minus } from '@element-plus/icons-vue'
import { ref, defineProps, defineEmits, reactive } from 'vue'
import Layer from '@/components/layer/index.vue'
import { ElMessage, ElLoading } from 'element-plus'
import {unitGroupSaveParams} from "@/type/units.type";
import {unitGroupSave, unitGroupUpdate} from "@/api/units";
type UnitsType = {
  name: string,
  value: number
}
const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  },
  type: {
    type: Number,
    required: true,
    validator: (value: number) => [1, 2].includes(value)
  }
})
const emit = defineEmits(['getTableData'])
const formRef = ref<FormInstance>()
const layerDom: Ref<LayerType | null> = ref(null)
const form = ref({
  base: '',
  groupName: '',
  scaleList: [],
  unitNameList: []
})
const units = ref<UnitsType[]>([]);

const addUnit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      units.value.push({ name: '', value: 0 });
    } else {
      console.log('请填写基本单位')
    }
  })
};

const removeUnit = (index: number) => {
  units.value.splice(index, 1);
};

const rules = ref({
  groupName: [
    { required: true, message: '请输入基本单位', trigger: 'blur' }
  ],
  base: [
    { required: true, message: '请输入单位名称', trigger: 'blur' }
  ],
});
let loading: any = null;
const submit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return;
  try {
    const valid = await formEl.validate();
    if (valid) {
      units.value.unshift({
        name: form.value.base,
        value: 1
      });
      units.value.reverse();
      loading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const paramsObj = createParamsObj(units.value);
      if (props.layer.row) {
        await updateForm(paramsObj);
      } else {
        await addForm(paramsObj);
      }
    }
  } catch (fieldsError) {
		console.log('error submit!', fieldsError);
  }
};

const createParamsObj = (params: UnitsType[]): unitGroupSaveParams => {
  const unitNames = params.map(item => item.name).join(',');
  const scales = params.map(item => item.value).join(',');
  return {
    ...form.value,
    groupName: form.value.groupName,
    base: form.value.base,
    unitNames,
    scales,
    mark: `${unitNames}(${scales})`,
    type: props.type, // 添加单位类型参数
  };
};

const addForm = async (paramsObj: unitGroupSaveParams) => {
  try {
    await unitGroupSave(paramsObj);
    ElMessage({
      type: 'success',
      message: '新增成功'
    });
    emit('getTableData', false);
    layerDom.value && layerDom.value.close();
  } catch (error) {
    // Handle error
  } finally {
    loading.close()
  }
};

const updateForm = async (params: unitGroupSaveParams) => {
  Reflect.deleteProperty(params, 'unitNameList')
  Reflect.deleteProperty(params, 'scaleList')
  unitGroupUpdate(params)
      .then(res => {
        ElMessage({
          type: 'success',
          message: '操作成功'
        })
        emit('getTableData', false)
        layerDom.value && layerDom.value.close()
      }).finally(() => {
        loading.close()
      })
}

function init() { // 用于判断新增还是编辑功能
  if (props.layer.row) {
    form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
    units.value = form.value.scaleList.map((value, index) => {
      return { name: form.value.unitNameList[index], value: value };
    });
    /* 去除基本单位弹出最后一个
        剩余单位倒序过来大到小 */
    units.value.pop()
    units.value.reverse()
  } else {
  }
}
init()

</script>

<style lang="scss" scoped>
:deep(.my-input .el-input__inner) {
  height: 40px;
}
</style>