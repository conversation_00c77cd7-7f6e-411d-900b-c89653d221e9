// usePrintXlsx.ts

interface Options {
  page?: 'A4' | 'Letter'
  landscape?: boolean
  showModal?: boolean        // Print.js 自带加载提示
  raw?: boolean              // 仅返回 HTML，不立刻打印
}

// 声明全局类型，避免 TypeScript 错误
declare global {
  interface Window {
    XLSX: any;
    printJS: any;
  }
}

// 手动构建表格函数，确保内容完整
function buildTableManually(ws: any, sheetName: string, index: number): string {
  if (!ws['!ref']) {
    return `<section><h2>${sheetName}</h2><p>此工作表为空</p></section>`
  }

  const range = ws['!ref'].split(':')
  const startCell = range[0] || 'A1'
  const endCell = range[1] || startCell

  // 解析范围
  const startCol = startCell.match(/[A-Z]+/)?.[0] || 'A'
  const startRow = parseInt(startCell.match(/\d+/)?.[0] || '1')
  const endCol = endCell.match(/[A-Z]+/)?.[0] || 'A'
  const endRow = parseInt(endCell.match(/\d+/)?.[0] || '1')

  // 转换列字母为数字
  const colToNum = (col: string) => {
    let result = 0
    for (let i = 0; i < col.length; i++) {
      result = result * 26 + (col.charCodeAt(i) - 64)
    }
    return result
  }

  const numToCol = (num: number) => {
    let result = ''
    while (num > 0) {
      num--
      result = String.fromCharCode(65 + (num % 26)) + result
      num = Math.floor(num / 26)
    }
    return result
  }

  const startColNum = colToNum(startCol)
  const endColNum = colToNum(endCol)

  let tableHtml = '<table>'

  // 构建表格行
  for (let row = startRow; row <= endRow; row++) {
    tableHtml += '<tr>'
    for (let colNum = startColNum; colNum <= endColNum; colNum++) {
      const col = numToCol(colNum)
      const cellAddress = `${col}${row}`
      const cell = ws[cellAddress]

      let cellValue = ''
      let cellAttrs = ''
      let cellClass = ''

      if (cell) {
        // 获取单元格值
        if (cell.v !== undefined) {
          cellValue = String(cell.v)
        } else if (cell.w !== undefined) {
          cellValue = String(cell.w)
        } else if (cell.h !== undefined) {
          cellValue = String(cell.h)
        }

        // 处理单元格类型和格式
        if (cell.t === 'n' && cell.z) {
          // 数字格式
          cellClass += ' excel-right'
        } else if (cell.t === 's') {
          // 字符串格式
          cellClass += ' excel-left'
        }
      }

      // 处理合并单元格
      let skipCell = false
      if (ws['!merges']) {
        for (const merge of ws['!merges']) {
          const mergeStartRow = merge.s.r + 1
          const mergeStartCol = merge.s.c + 1
          const mergeEndRow = merge.e.r + 1
          const mergeEndCol = merge.e.c + 1

          // 检查当前单元格是否在合并范围内
          if (row >= mergeStartRow && row <= mergeEndRow &&
              colNum >= mergeStartCol && colNum <= mergeEndCol) {

            if (row === mergeStartRow && colNum === mergeStartCol) {
              // 这是合并单元格的起始位置
              const colspan = mergeEndCol - mergeStartCol + 1
              const rowspan = mergeEndRow - mergeStartRow + 1
              if (colspan > 1) cellAttrs += ` colspan="${colspan}"`
              if (rowspan > 1) cellAttrs += ` rowspan="${rowspan}"`
            } else {
              // 这是合并单元格的其他位置，跳过
              skipCell = true
            }
          }
        }
      }

      if (!skipCell) {
        tableHtml += `<td${cellAttrs}${cellClass ? ` class="${cellClass.trim()}"` : ''}>${cellValue || '&nbsp;'}</td>`
      }
    }
    tableHtml += '</tr>'
  }

  tableHtml += '</table>'

  return `
    <section style="page-break-before:${index ? 'always' : 'auto'}">
      <h2 style="margin:8px 0 4px">${sheetName}</h2>${tableHtml}
    </section>`
}

export async function printXlsxBlob(blob: Blob, opt: Options = {}) {
  // 动态导入依赖
  const XLSX = await import('xlsx')
  const { default: printJS } = await import('print-js')

  // 1) 解析工作簿
  const arrayBuf = await blob.arrayBuffer()
  const wb = XLSX.read(arrayBuf, { type: 'array' })

  // 2) 逐 Sheet 转 HTML，插入分页
  const sheetsHtml = wb.SheetNames.map((name: string, i: number) => {
    const ws = wb.Sheets[name]

    // 检查工作表是否存在且有数据
    if (!ws || !ws['!ref']) {
      console.warn(`Sheet "${name}" 为空，跳过`)
      return ''
    }

    try {
      // 使用更详细的选项来保持格式
      const table = XLSX.utils.sheet_to_html(ws, {
        id: `sheet_${i}`,
        editable: false,
        header: '',
        footer: ''
      })
      return `
        <section style="page-break-before:${i ? 'always' : 'auto'}">
          <h2 style="margin:8px 0 4px">${name}</h2>${table}
        </section>`
    } catch (e) {
      console.error(`处理 Sheet "${name}" 时出错:`, e)
      // 如果自动转换失败，手动构建表格
      try {
        return buildTableManually(ws, name, i)
      } catch (e2) {
        console.error(`手动构建表格也失败:`, e2)
        return ''
      }
    }
  }).filter(html => html).join('')

  if (!sheetsHtml) {
    throw new Error('Excel 文件没有有效数据')
  }

  // 3) 全局打印样式 - 优化清晰度
  const pageSize = opt.page || 'A4'
  const css = `
    <style>
      @page { size: ${pageSize}${opt.landscape ? ' landscape' : ''}; margin:16mm; }
      body {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
        font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif;
        font-size: 12px;
        line-height: 1.4;
        margin: 0;
        padding: 0;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        page-break-inside: auto;
        margin-bottom: 10px;
        font-size: 12px;
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
      }
      th, td {
        border: 1px solid #000;
        padding: 4px 6px;
        text-align: center;
        vertical-align: middle;
        word-wrap: break-word;
        font-size: 12px;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
      }
      thead { display: table-header-group; }
      tr { page-break-inside: avoid; }
      h2 {
        margin: 8px 0 4px;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
      }
      section { margin-bottom: 20px; }

      /* 特殊样式用于保持Excel格式 */
      .excel-header { font-weight: bold; background-color: #f0f0f0; }
      .excel-center { text-align: center; }
      .excel-left { text-align: left; }
      .excel-right { text-align: right; }
    </style>`

  const htmlDoc = `${css}${sheetsHtml}`
  if (opt.raw) return htmlDoc                    // 调试/二次处理需要

  // 4) Print.js 打印 raw HTML
  printJS({ printable: htmlDoc, type: 'raw-html', showModal: opt.showModal })
}

// 备用：使用 print-js 进行HTML打印
export async function rawPrint(html: string) {
  const { safePrintHTML } = await import('./safePrint')

  try {
    await safePrintHTML(html, {
      showModal: true,         // 显示打印加载提示
      documentTitle: 'Excel打印',
      showActivationTip: false, // 在这里不显示提示，由调用方处理
      printJSOptions: {
        modalMessage: '正在准备打印Excel内容...'
      }
    })
  } catch (error) {
    console.error('[usePrintXlsx] 打印失败:', error)
    throw error
  }
}
