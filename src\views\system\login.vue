<template>
  <div class="login-container">
    <!-- Decorative background circles -->
    <div class="bg-circle circle-1"></div>
    <div class="bg-circle circle-2"></div>
    <div class="bg-circle circle-3"></div>
    <div class="bg-circle circle-4"></div>
    <div class="bg-circle circle-5"></div>

    <div class="login-wrapper">
      <div class="login-card">
        <div class="login-grid">
          <!-- Left Section - Hero -->
          <div class="hero-section">
            <!-- Background decoration -->
            <div class="hero-decoration">
              <div class="decoration-1"></div>
              <div class="decoration-2"></div>
            </div>

            <!-- Laptop illustration -->
            <div class="laptop-illustration">
              <div class="laptop-screen"></div>
              <div class="laptop-base"></div>
            </div>

            <div class="hero-content">
              <h1 class="hero-title">{{ $t(systemTitle) }}</h1>
              <p class="hero-subtitle">{{ $t(systemSubTitle) }}</p>
            </div>
          </div>

          <!-- Right Section - Login Form -->
          <div class="form-section">
            <div class="form-container">
              <div class="form-header">
                <h2 class="form-title">{{ $t('message.system.welcome') }}</h2>
                <p class="form-subtitle">请输入您的账户信息</p>
              </div>

              <form @submit.prevent="submit" class="login-form">
                <!-- Username Field -->
                <div class="input-group">
                  <label for="username" class="input-label">
                    {{ $t('message.system.userName') }}
                  </label>
                  <div class="input-wrapper">
                    <svg
                      class="input-icon"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <path
                        d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
                      ></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    <input
                      id="username"
                      type="text"
                      v-model="form.name"
                      class="form-input"
                      :placeholder="$t('message.system.userName')"
                      maxlength="50"
                    />
                  </div>
                </div>

                <!-- Password Field -->
                <div class="input-group">
                  <label for="password" class="input-label">
                    {{ $t('message.system.password') }}
                  </label>
                  <div class="input-wrapper">
                    <svg
                      class="input-icon"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                    >
                      <rect
                        x="3"
                        y="11"
                        width="18"
                        height="11"
                        rx="2"
                        ry="2"
                      ></rect>
                      <circle cx="12" cy="16" r="1"></circle>
                      <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                    </svg>
                    <input
                      id="password"
                      :type="showPassword ? 'text' : 'password'"
                      v-model="form.pwd"
                      class="form-input password-input"
                      :placeholder="$t('message.system.password')"
                      maxlength="50"
                    />
                    <button
                      type="button"
                      @click="togglePassword"
                      class="password-toggle"
                    >
                      <svg
                        v-if="showPassword"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path
                          d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"
                        ></path>
                        <line x1="1" y1="1" x2="23" y2="23"></line>
                      </svg>
                      <svg
                        v-else
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path
                          d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"
                        ></path>
                        <circle cx="12" cy="12" r="3"></circle>
                      </svg>
                    </button>
                  </div>
                </div>

                <!-- Remember & Forgot -->
                <div class="form-options">
                  <label class="remember-checkbox">
                    <input type="checkbox" v-model="rememberMe" />
                    <span class="checkmark"></span>
                    <span class="checkbox-label">记住密码</span>
                  </label>
                  <a
                    href="#"
                    @click.prevent="handleForgotPassword"
                    class="forgot-link"
                  >
                    忘记密码？
                  </a>
                </div>

                <!-- Login Button -->
                <button
                  type="submit"
                  class="login-button"
                  :disabled="form.loading"
                >
                  <span v-if="!form.loading">
                    {{ $t('message.system.login') }}
                  </span>
                  <span v-else>登录中...</span>
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import { systemTitle, systemSubTitle } from '@/config'
  import { defineComponent, ref, reactive } from 'vue'
  import { useStore } from 'vuex'
  import { useRouter, useRoute } from 'vue-router'
  import type { RouteLocationRaw } from 'vue-router'
  import { getAuthRoutes } from '@/router/permission'
  import { ElMessage } from 'element-plus'
  import selectLang from '@/layout/Header/functionList/word.vue'

  import { getCode } from '@/api/user'
  export default defineComponent({
    components: {
      selectLang,
    },
    setup() {
      const store = useStore()
      const router = useRouter()
      const route = useRoute()
      const form = reactive({
        name: '',
        pwd: '',
        loading: false,
        code: '',
        key: '',
      })
      const code = ref('')
      getCode().then(res => {
        form.key = res.key
        code.value = res.code
      })

      // 响应式数据
      const showPassword = ref(false)
      const rememberMe = ref(false)

      // 初始化时从本地存储加载记住的密码
      const loadRememberedPassword = () => {
        const remembered = localStorage.getItem('rememberedLogin')
        if (remembered) {
          const loginData = JSON.parse(remembered)
          form.name = loginData.username || ''
          form.pwd = loginData.password || ''
          rememberMe.value = true
        }
      }

      // 保存或清除记住的密码
      const saveRememberedPassword = () => {
        if (rememberMe.value) {
          const loginData = {
            username: form.name,
            password: form.pwd,
          }
          localStorage.setItem('rememberedLogin', JSON.stringify(loginData))
        } else {
          localStorage.removeItem('rememberedLogin')
        }
      }

      // 方法
      const togglePassword = () => {
        showPassword.value = !showPassword.value
      }

      const handleForgotPassword = () => {
        ElMessage.info({
          message: '请联系管理员重置密码',
          type: 'info',
          duration: 3000,
        })
      }

      // 初始化加载记住的密码
      loadRememberedPassword()
      const checkForm = () => {
        return new Promise((resolve, reject) => {
          if (form.name === '') {
            ElMessage.warning({
              message: '用户名不能为空',
              type: 'warning',
            })
            return
          }
          if (form.pwd === '') {
            ElMessage.warning({
              message: '密码不能为空',
              type: 'warning',
            })
            return
          }
          /*if (form.code === '') {
          ElMessage.warning({
            message: '验证码不能为空',
            type: 'warning'
          })
          return;
        }*/
          resolve(true)
        })
      }
      const submit = () => {
        checkForm().then(() => {
          form.loading = true
          let params = {
            account: form.name,
            pwd: form.pwd,
            key: form.key,
            code: form.code,
          }
          store
            .dispatch('user/login', params)
            .then(async () => {
              // 登录成功后保存记住的密码
              saveRememberedPassword()

              ElMessage.success({
                message: '登录成功',
                type: 'success',
                showClose: true,
                duration: 1000,
              })
              await store.dispatch('user/getImgUrl')
              location.reload()
              // await getAuthRoutes()
              // await router.push(route.query.redirect as RouteLocationRaw || '/')
            })
            .finally(() => {
              form.loading = false
            })
        })
      }
      return {
        code,
        systemTitle,
        systemSubTitle,
        form,
        showPassword,
        rememberMe,
        togglePassword,
        handleForgotPassword,
        submit,
      }
    },
  })
</script>

<style scoped>
  /* 基础样式 */
  * {
    box-sizing: border-box;
  }

  .login-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #dbeafe 0%, #f3e8ff 50%, #dbeafe 100%);
    position: relative;
    overflow: hidden;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* 装饰性背景圆圈 */
  .bg-circle {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
  }

  .circle-1 {
    top: 2.5rem;
    left: 2.5rem;
    width: 5rem;
    height: 5rem;
    background: rgba(147, 197, 253, 0.3);
  }

  .circle-2 {
    top: 8rem;
    left: 8rem;
    width: 8rem;
    height: 8rem;
    background: rgba(147, 197, 253, 0.2);
    filter: blur(80px);
  }

  .circle-3 {
    bottom: 5rem;
    left: 5rem;
    width: 6rem;
    height: 6rem;
    background: rgba(196, 181, 253, 0.25);
  }

  .circle-4 {
    top: 5rem;
    right: 5rem;
    width: 7rem;
    height: 7rem;
    background: rgba(147, 197, 253, 0.2);
    filter: blur(80px);
  }

  .circle-5 {
    bottom: 8rem;
    right: 8rem;
    width: 9rem;
    height: 9rem;
    background: rgba(233, 213, 255, 0.3);
    filter: blur(80px);
  }

  /* 登录卡片 */
  .login-wrapper {
    width: 100%;
    max-width: 72rem;
  }

  .login-card {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(12px);
    box-shadow:
      0 20px 40px -8px rgba(0, 0, 0, 0.15),
      0 8px 16px -4px rgba(0, 0, 0, 0.1);
    border-radius: 1.25rem;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .login-grid {
    display: grid;
    min-height: 520px;
  }

  /* 左侧英雄区域 */
  .hero-section {
    background: linear-gradient(135deg, #22d3ee 0%, #14b8a6 50%, #2563eb 100%);
    padding: 3rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: white;
    position: relative;
    overflow: hidden;
  }

  .hero-decoration {
    position: absolute;
    inset: 0;
    opacity: 0.1;
  }

  .decoration-1 {
    position: absolute;
    top: 25%;
    left: 25%;
    width: 16rem;
    height: 10rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 1rem;
    transform: rotate(12deg);
    filter: blur(4px);
  }

  .decoration-2 {
    position: absolute;
    bottom: 33.333333%;
    right: 25%;
    width: 12rem;
    height: 8rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 1rem;
    transform: rotate(-6deg);
    filter: blur(4px);
  }

  .laptop-illustration {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
    opacity: 0.2;
  }

  .laptop-screen {
    width: 8rem;
    height: 5rem;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 0.5rem;
  }

  .laptop-base {
    width: 9rem;
    height: 0.5rem;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 9999px;
    margin: 0.25rem auto 0;
  }

  .hero-content {
    position: relative;
    z-index: 10;
  }

  .hero-title {
    font-size: 3rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    line-height: 1.2;
  }

  .hero-subtitle {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.4;
    max-width: 32rem;
    white-space: nowrap;
  }

  /* 右侧表单区域 */
  .form-section {
    padding: 2.5rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.1) 0%,
      rgba(255, 255, 255, 0.05) 100%
    );
  }

  .form-container {
    width: 100%;
    max-width: 22rem;
    margin: 0 auto;
  }

  .form-header {
    text-align: center;
    margin-bottom: 1.75rem;
  }

  .form-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.375rem;
    letter-spacing: -0.025em;
  }

  .form-subtitle {
    color: #6b7280;
    font-size: 0.9375rem;
    font-weight: 400;
  }

  .login-form {
    display: flex;
    flex-direction: column;
    gap: 1.375rem;
  }

  /* 输入框样式 */
  .input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .input-label {
    font-size: 0.8125rem;
    font-weight: 500;
    color: #374151;
    text-align: left;
  }

  .input-wrapper {
    position: relative;
  }

  .input-icon {
    position: absolute;
    left: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    width: 1.125rem;
    height: 1.125rem;
    stroke-width: 2;
    transition: color 0.2s ease;
  }

  .form-input {
    width: 100%;
    height: 2.75rem;
    padding-left: 2.375rem;
    padding-right: 0.875rem;
    background: rgba(249, 250, 251, 0.8);
    border: 1px solid rgba(229, 231, 235, 0.8);
    border-radius: 0.625rem;
    font-size: 0.9375rem;
    font-weight: 400;
    color: #374151;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .password-input {
    padding-right: 2.375rem;
  }

  .form-input:focus {
    outline: none;
    background: rgba(255, 255, 255, 0.95);
    border-color: #3b82f6;
    box-shadow:
      0 0 0 3px rgba(59, 130, 246, 0.08),
      0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .form-input:focus + .input-icon {
    color: #3b82f6;
  }

  .password-toggle {
    position: absolute;
    right: 0.875rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.125rem;
    width: 1.125rem;
    height: 1.125rem;
    transition: all 0.2s ease;
    border-radius: 0.25rem;
  }

  .password-toggle:hover {
    color: #6b7280;
    background: rgba(107, 114, 128, 0.1);
  }

  .password-toggle svg {
    width: 100%;
    height: 100%;
    stroke-width: 2;
  }

  /* 表单选项 */
  .form-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 0.8125rem;
    margin-top: -0.125rem;
  }

  .remember-checkbox {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.375rem;
    transition: background-color 0.2s ease;
  }

  .remember-checkbox:hover {
    background: rgba(59, 130, 246, 0.05);
  }

  .remember-checkbox input[type='checkbox'] {
    width: 0.875rem;
    height: 0.875rem;
    border-radius: 0.1875rem;
    border: 1px solid #d1d5db;
    accent-color: #3b82f6;
    cursor: pointer;
  }

  .checkbox-label {
    color: #6b7280;
    font-size: 0.8125rem;
    cursor: pointer;
  }

  .forgot-link {
    color: #3b82f6;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 0.8125rem;
    padding: 0.25rem 0.375rem;
    border-radius: 0.375rem;
  }

  .forgot-link:hover {
    color: #1d4ed8;
    background: rgba(59, 130, 246, 0.05);
  }

  /* 登录按钮 */
  .login-button {
    width: 100%;
    height: 2.75rem;
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
    font-weight: 500;
    border: none;
    border-radius: 0.625rem;
    box-shadow:
      0 4px 12px rgba(59, 130, 246, 0.25),
      0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9375rem;
    letter-spacing: 0.025em;
    margin-top: 0.5rem;
  }

  .login-button:hover:not(:disabled) {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
    box-shadow:
      0 6px 16px rgba(59, 130, 246, 0.35),
      0 4px 8px rgba(0, 0, 0, 0.15);
    transform: translateY(-1px);
  }

  .login-button:active:not(:disabled) {
    transform: translateY(0);
    box-shadow:
      0 2px 8px rgba(59, 130, 246, 0.3),
      0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .login-button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    background: #9ca3af;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  /* 响应式设计 */
  @media (min-width: 1024px) {
    .login-grid {
      grid-template-columns: 1fr 1fr;
    }

    .hero-title {
      font-size: 3.75rem;
    }

    .hero-subtitle {
      font-size: 1.125rem;
    }
  }

  @media (max-width: 1023px) {
    .login-grid {
      grid-template-columns: 1fr;
    }

    .hero-section {
      padding: 2rem;
      min-height: 300px;
    }

    .form-section {
      padding: 2rem;
    }

    .hero-title {
      font-size: 2.5rem;
    }

    .hero-subtitle {
      font-size: 0.9375rem;
      white-space: normal;
    }
  }

  @media (max-width: 640px) {
    .login-container {
      padding: 0.5rem;
    }

    .hero-section {
      padding: 1.5rem;
    }

    .form-section {
      padding: 1.5rem;
    }

    .hero-title {
      font-size: 2rem;
    }

    .hero-subtitle {
      font-size: 0.875rem;
      white-space: normal;
    }

    .form-container {
      max-width: 20rem;
    }
  }
</style>
