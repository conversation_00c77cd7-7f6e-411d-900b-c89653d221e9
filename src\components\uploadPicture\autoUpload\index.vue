<template>
    <!-- 封装弹框 -->
    <div>
        <el-dialog :title="props.title" v-model="dialogPopVisible" :before-close="onBeforeClose" :center="true" width="60vw"
            :modal-append-to-body="false">
            <el-row :gutter="30">
                <el-col :span="7">
                    <div><el-input v-model.trim="leftData.searchInput" placeholder="请输入分类" clearable @change="getTreeData">
                            <template #append>
                                <el-button :icon="Search" @click="getTreeData" />
                            </template></el-input>
                    </div>
                    <div style="padding-top: 10px;max-height: 500px;overflow:auto;">
                        <el-tree ref="treeRef" :data="leftData.tree.data" :props="leftData.tree.defaultProps">
                            <template #default="{ node, data }">
                                <span class="custom-tree-node">
                                    <span @click="nodeClick(data)">{{ node.label }}</span>
                                    <span>
                                        <el-dropdown>
                                            <span class="el-dropdown-link">
                                                <el-icon>
                                                    <More />
                                                </el-icon>
                                            </span>
                                            <template #dropdown>
                                                <el-dropdown-menu>
                                                    <el-dropdown-item
                                                        @click="treeDropDown.add(data)">添加分类</el-dropdown-item>
                                                    <el-dropdown-item @click="treeDropDown.update(data)">
                                                        修改分类</el-dropdown-item>
                                                    <el-dropdown-item @click="treeDropDown.delete(data)"
                                                        v-if="data.id != ''">删除分类</el-dropdown-item>
                                                </el-dropdown-menu>
                                            </template>
                                        </el-dropdown>
                                    </span>
                                </span>
                            </template>
                        </el-tree>
                    </div>
                </el-col>
                <el-col :span="17">
                    <div class="header">
                        <span style="display: flex; align-items: center;">可选&nbsp; <span :style="{ color: '#409EFF' }">{{
                            props.size
                        }}</span> &nbsp;张，已选&nbsp;<span
                                :style="{ color: picture.checkList.length > props.size ? '#F56C6C' : '#409EFF' }">{{
                                    picture.checkList.length }}</span>&nbsp; 张</span>


                        <el-button type="primary" @click="emitData"><el-icon class="mr3">
                                <DocumentChecked />
                            </el-icon>使用选中图片/视频</el-button>
                        <el-upload action='' v-model:file-list="fileList" :on-change="imgSaveToUrl" :show-file-list="false"
                            multiple>
                            <el-button class="mr3"><el-icon>
                                    <Upload />
                                </el-icon>上传图片/视频</el-button>
                        </el-upload>

                        <el-button type="danger" @click="deletePic" class="mr3"><el-icon>
                                <DeleteFilled />
                            </el-icon>删除图片</el-button>
                        <el-radio-group v-model="typeDate" @change="typeChange">
                            <el-radio-button label="pic">图片</el-radio-button>
                            <el-radio-button label="video">视频</el-radio-button>
                        </el-radio-group>
                    </div>
                    <div style="display: flex; justify-content: flex-end; align-items: center;margin-top: 10px;"> <span>
                            <el-cascader :options="leftData.tree.data" v-model="moveId" clearable :show-all-levels="false"
                                :props="moveCategoryProps" placeholder="请选择分类">
                            </el-cascader> <el-button type="primary" @click="moveCategory">移动到该分类</el-button>
                        </span></div>
                    <div class="piclist">
                        <div v-show="picture.list.length === 0" style="height:100%;width: 100%;">
                            <el-empty description="暂无图片" style="height:100%;" />
                        </div>
                        <div v-show="picture.list.length != 0">
                            <div class="list">
                                <div v-for="item in picture.list" class="conters" @click="clickPic(item)">
                                    <el-image :src="picUrl + item.sattDir" fit="cover" class="picture" v-if="typeDate === 'pic'"
                                        :class="item.isSelect ? 'on' : ''">
                                        <template #error>
                                            <div class="image-slot">
                                                <el-icon>
                                                    <Picture />
                                                </el-icon>
                                            </div>
                                        </template>
                                    </el-image>
                                    <video class="picture" :src="picUrl + item.sattDir" :class="item.isSelect ? 'on' : ''"
                                        v-else></video>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex;justify-content: center;">
                        <el-pagination v-model:current-page="page.page" v-model:page-size="page.limit"
                            :page-sizes="[10, 20, 40, 80, 1000]" layout="total, sizes, prev, pager, next, jumper"
                            :total="page.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
                    </div>
                </el-col>
            </el-row>

        </el-dialog>
        <el-dialog v-model="treeDialog.show" :title="treeDialog.title" center>
            <el-form :model="treeDialog.form" labelWidth="120px">
                <el-form-item label="上级分类">
                    <el-cascader :options="leftData.tree.data" v-model="treeDialog.form.pid"
                        :props="treeDialog.categoryProps">
                    </el-cascader>
                </el-form-item>
                <el-form-item label="分类名称">
                    <el-input v-model="treeDialog.form.name" placeholder="请输入分类名称" />
                </el-form-item>
                <el-form-item label="排序">
                    <el-input-number v-model="treeDialog.form.sort" :min="1" :step="1">
                    </el-input-number>
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="treeDialog.show = false">取消</el-button>
                    <el-button type="primary" @click="treeDropDown.handEnter">
                        确定
                    </el-button>
                </span>
            </template>
        </el-dialog>
    </div>
</template>
<script setup lang="ts">
import type { Category, leftData, TreeDialog, PictureTs, PictureObj } from './interface'

import type { UploadProps, UploadUserFile } from 'element-plus'
import { ref, defineProps, reactive } from "vue";
import {
    ElLoading, ElUpload, ElPagination, ElImage, ElEmpty, ElRadioGroup, ElRadioButton, ElCascader, ElInputNumber, ElForm, ElFormItem,
    ElMessage, ElMessageBox, ElDialog, ElRow, ElCol, ElInput, ElButton, ElIcon, ElTree, ElDropdown, ElDropdownItem, ElDropdownMenu
} from 'element-plus'
import { Search, More, Upload, DeleteFilled, DocumentChecked, Picture } from '@element-plus/icons-vue'
import { deleteCategroy, treeCategroy, addCategroy, updateCategroy } from "@/api/categoryApi";
import {
    fileImageApi,
    fileListApi,
    fileDeleteApi,
    attachmentMoveApi
} from "@/api/pictureUpload";
import { getPicUrl } from '@/utils/index'
const picUrl = getPicUrl()
/* 弹框传值相关 */
const props = defineProps({
    saveClick: {
        type: Function,
        default: () => { },
    },
    size: {
        type: Number,
        default: 1
    },
    title: {
        type: String,
        default: '上传图片'
    }
});
const dialogPopVisible = ref(true); // 窗体显示控制

const onBeforeClose = (done: any) => {
    done();
};

/* 左侧分类相关 */
const leftData = reactive<leftData>({
    searchInput: '',
    tree: {
        data: [],
        defaultProps: {
            children: "child",
            label: "name",
        },
    }
})
const treeRef = ref<InstanceType<typeof ElTree>>()
const getTreeData = () => {
    let datas: Category = {
        name: '全部图片',
        id: 0,
        pid: 0,
        sort: 1
    }
    treeCategroy({ status: -1, type: 2, name: leftData.searchInput }).then(res => {
        leftData.tree.data = res
        leftData.tree.data.unshift(datas)
    })
}
getTreeData()
/* 树状图下拉菜单 */
const treeDialog = reactive<TreeDialog>({
    show: false,
    title: '添加分类',
    form: {
        pid: 0,
        name: '',
        type: 2,
        sort: 1,
        url: 'url',
        id: 0,
        extra: '',
        status: true
    },
    categoryProps: {
        value: "id",
        label: "name",
        children: "child",
        checkStrictly: true,
        emitPath: false,
    },
})
const treeDropDown = {
    delete: (data: Category) => {
        ElMessageBox.confirm(
            '是否删除该分类',
            '提示',
            {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }
        )
            .then(() => {
                deleteCategroy({ id: data.id }).then(res => {
                    ElMessage({
                        type: 'success',
                        message: '删除成功'
                    })
                    getTreeData()
                })
            })
    },
    update: (data: Category) => {
        treeDialog.show = true,
            treeDialog.title = '修改分类'
        treeDialog.form.pid = data.pid
        treeDialog.form.id = data.id
        treeDialog.form.name = data.name
    },
    add: (data: Category) => {
        treeDialog.show = true,
            treeDialog.title = '添加分类'
        treeDialog.form.pid = data.id
    },
    handEnter: () => {
        if (treeDialog.title === '添加分类') {
            addCategroy(treeDialog.form).then(res => {
                ElMessage({
                    type: 'success',
                    message: '添加成功'
                })
                treeDialog.show = false
                getTreeData()
            })
        } else {
            updateCategroy(treeDialog.form).then(res => {
                ElMessage({
                    type: 'success',
                    message: '修改成功'
                })
                treeDialog.show = false
                getTreeData()
            })
        }
    }
}
const nodeClick = (data: any) => {
    page.pid = data.id
    getPicList()
}
/* 右侧图片相关 */
const typeDate = ref('pic')
const page = reactive({
    page: 1,
    limit: 20,
    total: 0,
    attType: "jpg,jpeg,gif,png,bmp,PNG,JPG",
    pid: 0,
})
const picture = reactive<PictureTs>({
    list: [],
    checkList: []
})
const getPicList = () => {
    fileListApi(page).then(res => {
        picture.list = res.list
        page.total = res.total
    })
}
const typeChange = () => {
    if (typeDate.value === 'pic') {
        page.attType = "jpg,jpeg,gif,png,bmp,PNG,JPG"
    } else {
        page.attType = "video/mp4"
    }
    getPicList()
}
const handleSizeChange = (val: number) => {
    page.limit = val
    getPicList()
}
const handleCurrentChange = (val: number) => {
    page.page = val
    getPicList()
}
getPicList()
const clickPic = (item: PictureObj) => {
    if (!item.isSelect) {
        item.isSelect = true
        picture.checkList.push(item)
    } else {
        item.isSelect = !item.isSelect
        picture.checkList = picture.checkList.filter((item: PictureObj) => {
            return item.isSelect
        })
    }

}
/* 传参回去 */
const emitData = () => {
    if (!picture.checkList.length) {
        ElMessage({
            type: 'warning',
            message: '请选择图片'
        })
        return
    }
    if (picture.checkList.length > props.size) {
        ElMessage({
            type: 'warning',
            message: '超过可选图片数'
        })
        return
    }
    props.saveClick(picture.checkList)
    dialogPopVisible.value = false


}

/* 移动分类 */
const moveId = ref()
const moveCategoryProps = {
    value: "id",
    label: "name",
    children: "child",
    checkStrictly: true,
    emitPath: false,
}
const moveCategory = () => {
    if (moveId.value === null || moveId.value === undefined) {
        ElMessage.warning('请选择分类')
        return
    }
    console.log(picture.checkList);

    let params = {
        pid: moveId.value,
        attrId: picture.checkList.map(item => item.attId).toString()
    }
    attachmentMoveApi(params).then(res => {
        ElMessage.success('移动成功')
        picture.checkList = []
        picture.list.forEach(item => {
            item.isSelect = false
        })
    })
}


const deletePic = () => {
    if (!picture.checkList.length) {
        ElMessage({
            type: 'warning',
            message: '请选择图片'
        })
        return
    } else {
        let ids = picture.checkList.map((item: PictureObj) => {
            return item.attId
        })
        fileDeleteApi(ids.toString()).then(res => {
            ElMessage({
                type: 'success',
                message: '删除成功'
            })
            picture.checkList = []
            page.page = 1,
                page.limit = 20
            getPicList()
        })
    }
}

const fileList = ref<UploadUserFile[]>([])
const imgSaveToUrl: UploadProps['onChange'] = (uploadFile, uploadFiles) => {
    fileList.value = []
    if (uploadFile.status !== 'ready') return;
    const loadingInstance = ElLoading.service({ fullscreen: true })
    fileList.value.push(uploadFile)
    const formData = new FormData();
    let data = {
        model: 'product',
        pid: page.pid
    }
    fileList.value.forEach(item => {
        formData.append("multipart", item.raw)
    })

    fileImageApi(formData, data).then(res => {
        ElMessage({
            type: 'success',
            message: '上传成功'
        })
        page.page = 1,
            getPicList()
    }).finally(() => {
        loadingInstance.close()
    })
}   
</script>
  
<style lang="scss" scoped>
:deep(.image-slot) {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, .02);
    color: var(--el-text-color-secondary);
    font-size: 30px;
    border: 1px dotted rgba(0, 0, 0, .1);
    border-radius: 4px;
}

.custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 14px;
    padding-right: 8px;
    color: #4386c6;
}

.header {
    display: flex;
    justify-content: space-around;

    .mr3 {
        margin-right: 3px;
    }

}

.piclist {
    height: 500px;
    max-height: 500px;
    padding-top: 10px;
    display: flex;
    overflow: auto;

    .picture {
        margin: 5px;
        height: 100px;
        width: 100px;
    }

    .list {
        display: flex;
        flex-wrap: wrap;

        .conters {
            display: flex;

            .on {
                border: 2px solid #1890FF !important;
                position: relative;

                &::before {
                    content: "";
                    position: absolute;
                    right: 0;
                    bottom: 0;
                    border: 10px solid #1890FF;
                    border-top-color: transparent;
                    border-left-color: transparent;

                }

                &:after {
                    content: "";
                    width: 5px;
                    height: 9px;
                    position: absolute;
                    right: 2px;
                    bottom: 2px;
                    border: 1px solid #fff;
                    border-top-color: transparent;
                    border-left-color: transparent;
                    transform: rotate(45deg);
                }
            }

            // .on::before {
            //     content: '1';
            //     position: absolute;
            //     right: 10px;
            //     bottom: 10px;
            //     width: 10px;
            //     height: 10px;
            //     border-top-color: transparent;
            //     border-left-color: transparent;
            // }

            .picture {
                margin: 5px;
                border: 2px solid white;
            }
        }
    }
}
</style>