<template>
  <div class="layout-container" style="padding: 15px">
    <!-- 搜索表单 -->
    <div style="text-align: left">
      <el-form label-width="100px" :inline="false">
        <el-form-item label="时间范围">
          <timePicker v-model="query.dateLimit" @change="getTableData(true)" />
        </el-form-item>

        <el-form-item label="出入库类型">
          <el-radio-group v-model="query.type" @change="getTableData(true)">
            <el-radio-button
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="原料选择">
          <el-select
            v-model="query.rawMaterialId"
            placeholder="请选择原料"
            clearable
            filterable
            @change="getTableData(true)"
            class="inputWidth"
          >
            <el-option label="全部原料" :value="0" />
            <el-option
              v-for="item in materialOptions"
              :key="item.id"
              :label="item.rawMaterialName"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </div>

		<div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button
      	  type="success"
      	  @click="handleExport"
      	  :loading="exportLoading"
      	>
      	  导出记录
      	</el-button>
      </div>
    </div>

    <!-- 表格 -->
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column prop="id" label="ID" align="center" width="80" />
        <el-table-column
          prop="rawMaterialName"
          label="原料名称"
          align="center"
        />
        <el-table-column label="类型" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.type === 1 ? 'success' : 'warning'">
              {{ scope.row.type === 1 ? '入库' : '出库' }}
            </el-tag>
          </template>
        </el-table-column>

				<el-table-column prop="beforeNum" label="修改前数量" align="center" />
        <el-table-column prop="num" label="修改数量" align="center">
          <template #default="scope">
            <span
              :class="scope.row.num !== null ? (scope.row.type === 1 ? 'text-success' : 'text-warning') : ''"
            >
              {{ scope.row.num !== null ? (scope.row.type === 1 ? '+' : '-') : '' }}{{ scope.row.num || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="afterNum" label="修改后数量" align="center" />

        <el-table-column prop="beforeStock" label="修改前库存" align="center">
          <template #default="scope">
            <span>{{ scope.row.beforeStock || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="changeStock" label="修改库存" align="center">
          <template #default="scope">
            <span
              :class="scope.row.type === 1 ? 'text-success' : 'text-warning'"
            >
              {{ scope.row.changeStock || '-' }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="afterStock" label="修改后库存" align="center">
          <template #default="scope">
            <span>{{ scope.row.afterStock || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column prop="createBy" label="操作人" align="center" />
      </Table>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted } from 'vue'
  import { Page } from '@/components/table/type'
  import Table from '@/components/table/index.vue'
  import timePicker from '@/components/timePicker/index.vue'
  import { getMaterialStockLogList, getMaterialList, exportMaterialStockLog } from '@/api/material'
  import type {
    MaterialStockLogParams,
    MaterialStockLogRes,
    MaterialListRes,
  } from '@/type/material.type'
  import { ElMessage } from 'element-plus'
  import store from '@/store'
  import { exportDataWithLoading, createExportConfig, exportDebounce } from '@/utils/exportUtils'

  // 搜索条件
  const query = reactive<MaterialStockLogParams>({
    page: 1,
    limit: 20,
    rawMaterialId: 0,
    type: 0,
    dateLimit: '',
  })

  // 分页参数
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })

  // 表格数据
  const loading = ref(true)
  const tableData = ref<MaterialStockLogRes[]>([])

  // 导出状态
  const exportLoading = ref(false)

  // 出入库类型选项
  const typeOptions = [
    { label: '全部', value: 0 },
    { label: '入库', value: 1 },
    { label: '出库', value: 2 },
  ]

  // 原料选项
  const materialOptions = ref<MaterialListRes[]>([])

  // 获取表格数据
  const getTableData = (init?: boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }

    const params: MaterialStockLogParams = {
      page: page.index,
      limit: page.size,
      ...(query.rawMaterialId !== 0 && { rawMaterialId: query.rawMaterialId }),
      ...(query.type !== 0 && { type: query.type }),
      ...(query.dateLimit && { dateLimit: query.dateLimit }),
    }

    getMaterialStockLogList(params)
      .then(res => {
        tableData.value = res.list
        page.total = Number(res.total)
      })
      .catch(error => {
        console.error('获取原料出入库记录失败:', error)
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 获取原料选项
  const getMaterialOptions = () => {
    getMaterialList({ page: 1, limit: 1000 })
      .then(res => {
        materialOptions.value = res.list
      })
      .catch(error => {
        console.error('获取原料列表失败:', error)
      })
  }

  // 原始导出函数
  const originalHandleExport = async () => {
    // 准备导出参数
    const exportParams = {
      ...(query.rawMaterialId && query.rawMaterialId !== 0 && query.rawMaterialId !== null && query.rawMaterialId !== undefined && { rawMaterialId: query.rawMaterialId }),
      type: query.type || 0,
      dateLimit: query.dateLimit || '',
    }

    // 使用通用导出函数
    const config = createExportConfig.materialStock(exportParams)
    await exportDataWithLoading(config, exportLoading)
  }

  // 使用防抖包装的导出函数
  const handleExport = exportDebounce(originalHandleExport, { delay: 500, minInterval: 1000 })

  // 初始化
  onMounted(() => {
    getMaterialOptions()
    getTableData(true)
  })
</script>

<style lang="scss" scoped>
  .text-success {
    color: #67c23a;
    font-weight: bold;
  }

  .text-warning {
    color: #e6a23c;
    font-weight: bold;
  }
</style>
