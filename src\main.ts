import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import { baidu } from './utils/system/statistics'
import 'element-plus/theme-chalk/display.css' // 引入基于断点的隐藏类
import 'element-plus/dist/index.css'
import 'normalize.css' // css初始化
import 'virtual:uno.css' // UnoCSS 虚拟模块
import './assets/style/common.scss' // 公共css
import './theme/modules/chinese/index.scss'
import App from './App.vue'
import store from './store'
import router from './router'
import { getAuthRoutes } from './router/permission'
import i18n from './locale'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { prepareZXingModule } from 'zxing-wasm/reader'

// 初始化 zxing-wasm
prepareZXingModule()
if (import.meta.env.MODE !== 'development') {
  // 非开发环境调用百度统计
  baidu()
}
import SubDialog from '@/components/uploadPicture/autoUpload'
import hasPermission from '@/utils/hasPermission'
/** 权限路由处理主方法 */
getAuthRoutes().then(() => {
  const app = createApp(App)
  app.use(ElementPlus, { size: store.state.app.elementSize })
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component)
  }
  app.use(store)
  app.use(router)
  app.use(i18n)
  app.use(hasPermission)
  // app.config.performance = true
  app.use(SubDialog)
  app.mount('#app')
})
