import { ref, onBeforeUnmount } from 'vue'
import {
  readBarcodes,
  type ReaderOptions
} from 'zxing-wasm/reader'

/** 扫码 Hook：返回 videoRef、result、start、stop */
export function useBarcodeScanner() {
  const videoEl = ref<HTMLVideoElement>()
  const result = ref<string | null>(null)
  const running = ref(false)
  const error = ref<string | null>(null)
  let stream: MediaStream | null = null
  let rafId = 0

  const readerOptions: ReaderOptions = {
    formats: [
      'QRCode',           // 二维码
      'EAN-13',           // 商品条码
      'EAN-8',            // 短商品条码
      'Code128',          // Code128条码
      'Code39',           // Code39条码
      'Code93',           // Code93条码
      'Codabar',          // Codabar条码
      'ITF',              // ITF条码
      'UPC-A',            // UPC-A条码
      'UPC-E',            // UPC-E条码
      'DataMatrix',       // 数据矩阵码
      'PDF417',           // PDF417码
      'Aztec'             // Aztec码
    ],
    tryHarder: true,
    maxNumberOfSymbols: 1
  }

  async function tick() {
    if (!videoEl.value || !running.value) return

    const { videoWidth: w, videoHeight: h } = videoEl.value
    if (w && h) {
      try {
        // 把当前帧画进 OffscreenCanvas，提升性能
        const ctx = new OffscreenCanvas(w, h)
          .getContext('2d') as OffscreenCanvasRenderingContext2D
        ctx.drawImage(videoEl.value, 0, 0, w, h)
        const imgData = ctx.getImageData(0, 0, w, h)

        // 尝试解码
        const res = await readBarcodes(imgData, readerOptions)
        if (res.length) {
          console.log('扫码成功:', res[0])
          result.value = res[0].text
          stop()
          return
        }
      } catch (err) {
        // 忽略解码错误，继续下一帧
        console.debug('扫码解析中...', err)
      }
    }

    // 降低扫描频率，避免过度消耗CPU
    setTimeout(() => {
      rafId = requestAnimationFrame(tick)
    }, 100) // 每100ms扫描一次
  }

  async function start() {
    if (running.value) return
    try {
      error.value = null
      result.value = null

      // 请求更高质量的摄像头设置，有助于条形码识别
      stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: { ideal: 'environment' },
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 }
        }
      })

      if (videoEl.value) {
        videoEl.value.srcObject = stream
        await videoEl.value.play()
        running.value = true
        console.log('摄像头启动成功，开始扫描...')
        tick()
      }
    } catch (err) {
      error.value = "无法启动摄像头。请检查并授予摄像头权限。"
      console.error('Camera start error:', err)
    }
  }

  function stop() {
    running.value = false
    cancelAnimationFrame(rafId)
    if (stream) {
      stream.getTracks().forEach(t => t.stop())
      stream = null
    }
  }

  // 从文件扫描二维码
  async function scanFromFile(file: File): Promise<string | null> {
    try {
      const res = await readBarcodes(file, readerOptions)
      return res.length > 0 ? res[0].text : null
    } catch (err) {
      console.error('File scan error:', err)
      return null
    }
  }

  // 切换闪光灯
  async function toggleFlashlight(): Promise<boolean> {
    if (!stream || !running.value) return false

    try {
      const track = stream.getVideoTracks()[0]
      const capabilities = track.getCapabilities() as any

      if (capabilities.torch) {
        const settings = track.getSettings() as any
        const newTorchState = !settings.torch
        await track.applyConstraints({
          advanced: [{ torch: newTorchState } as any]
        } as any)
        return newTorchState
      }
      return false
    } catch (err) {
      console.error('Flashlight toggle error:', err)
      return false
    }
  }

  onBeforeUnmount(stop)

  return { 
    videoEl, 
    result, 
    running, 
    error,
    start, 
    stop, 
    scanFromFile,
    toggleFlashlight
  }
}
