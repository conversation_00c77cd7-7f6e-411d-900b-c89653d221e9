<template>
  <div class="layout-container">
    <div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ $t('message.common.add') }}
        </el-button>
        <!-- <el-popconfirm :title="$t('message.common.delTip')" @confirm="handleDel(chooseData)">
          <template #reference>
            <el-button type="danger" :icon="Delete" :disabled="chooseData.length === 0">{{ $t('message.common.delBat')
            }}</el-button>
          </template>
        </el-popconfirm> -->
      </div>
      <div class="layout-container-form-search">
        <el-input
          v-model.trim="query.keywords"
          :placeholder="$t('message.common.searchTip')"
          @change="getTableData(true)"
        ></el-input>
        <el-button
          type="primary"
          :icon="Search"
          class="search-btn"
          @click="getTableData(true)"
        >
          {{ $t('message.common.search') }}
        </el-button>
      </div>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column prop="id" label="id" align="center" />
        <el-table-column prop="name" label="模板名称" align="center" />
        <el-table-column prop="type" label="计费方式" align="center">
          <template #default="scope">
            <div>
              <span v-if="scope.row.type == 1">按件数</span>
              <span v-else-if="scope.row.type == 2">按重量</span>
              <span v-else>按体积</span>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="appoint" label="指定包邮" align="center">
          <template #default="scope">
            <div>
              <el-tag v-if="scope.row.appoint" type="success">开启</el-tag>
              <el-tag v-if="!scope.row.appoint" type="danger">关闭</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" align="center" />
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column prop="updateTime" label="更新时间" align="center" />
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)">
              {{ $t('message.common.update') }}
            </el-button>
            <el-popconfirm
              :title="$t('message.common.delTip')"
              @confirm="handleDel([scope.row])"
            >
              <template #reference>
                <el-button type="danger">
                  {{ $t('message.common.del') }}
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </Table>
      <Layer
        :layer="layer"
        @getTableData="getTableData"
        v-if="layer.show"
        style="width: 80%; height: 80%"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { defineComponent, ref, reactive } from 'vue'
  import Table from '@/components/table/index.vue'
  import { Page } from '@/components/table/type'
  import Layer from './layer.vue'
  import { ElMessage } from 'element-plus'
  import type { LayerInterface } from '@/components/layer/index.vue'
  import { Plus, Search, Delete } from '@element-plus/icons-vue'
  import { getLogisticsList, deleteLogistics } from '@/api/logistics'
  import type { LogisticsListRes } from '@/type/logistics.type'

  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
    width: '80%',
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<LogisticsListRes[]>([])
  const chooseData = ref([])
  const handleSelectionChange = (val: []) => {
    chooseData.value = val
  }
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getLogisticsList(params)
      .then(res => {
        tableData.value = res.list

        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  // 删除功能
  const handleDel = (data: LogisticsListRes[]) => {
    let params = {
      id: data
        .map(e => {
          return e.id
        })
        .join(','),
    }
    deleteLogistics({ id: Number(params.id) }).then(res => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  // 新增弹窗功能
  const handleAdd = () => {
    layer.title = '新增数据'
    layer.show = true
    delete layer.row
  }
  // 编辑弹窗功能
  const handleEdit = (row: object) => {
    layer.title = '编辑数据'
    layer.row = row
    layer.show = true
  }
  getTableData(true)
</script>

<style lang="scss" scoped></style>
