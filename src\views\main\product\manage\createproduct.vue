<template>
    <div class="layout-container" style="padding: 15px;">
        <el-divider />
        <el-form :model="form" ref="ruleFormRef" :rules="rules" label-width="120px" :inline="false" style="margin: 20px;">
            <el-row>
                <el-col :sm="12" :xs="24">
                    <el-form-item label="产品名称" prop="productName">
                        <el-input v-model="form.productName"></el-input>
                    </el-form-item>
                </el-col>
                <el-col :sm="12" :xs="24">
                    <el-form-item label="产品分类" prop="cateId">
                        <el-cascader :options="options" :props="props" clearable style="width: 100%;"
                            v-model="form.cateId" />
                    </el-form-item>
                </el-col>
                <el-col :sm="12" :xs="24">
                    <el-form-item label="单位" prop="unitId">
                      <el-select 
                          v-model="form.unitId" 
                          @change="changeUnit" 
                          filterable
                          clearable
                      >
                        <el-option
                            v-for="item in unitOptions"
                            :key="item.id"
                            :label="item.groupName"
                            :value="item.id"
                        />
                      </el-select>
                    </el-form-item>
                </el-col>
                <el-col :sm="12" :xs="24">
                    <el-form-item label="产品封面图" prop="image">
                        <uploadIcon v-model="form.image" />
                    </el-form-item>
                </el-col>
                <!--<el-col :sm="12" :xs="24">
                    <el-form-item label="仓库轮播图" prop="sliderImage">
                        <uploadIcon v-model="form.sliderImage" :size="9" />
                    </el-form-item>
                </el-col>-->
								<el-col v-if="form.unitId && getUnitList && getUnitList.length">
									<el-form-item>
										<!-- 警戒库存设置区域 -->
										<div class="unit-group-section alert-stock-section">
											<div class="unit-group-title alert-stock-title mb-[.5rem]">
												<span class="title-with-icon">
													<svg class="alert-warning-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
														<path d="m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z"/>
														<path d="M12 9v4"/>
														<path d="m12 17 .01 0"/>
													</svg>
													<span class="text-[.8rem]">{{ unitOptions.find(item => item.id === form.unitId)?.groupName || '单位组' }} - 警戒库存设置</span>
												</span>
												<small class="unit-group-desc alert-stock-desc flex items-center">
													<svg class="desc-info-icon" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
														<circle cx="12" cy="12" r="10"/>
														<path d="m9 12 2 2 4-4"/>
													</svg>
													<span class="text-[.8rem]">设置各单位的警戒库存数量，当库存低于此值时将显示警戒提示</span>
												</small>
											</div>

											<!-- 单行组合警戒值输入 -->
											<div class="alert-combined-section">
												<div class="alert-units-inline">
													<span class="text-[.9rem]">警戒值：</span>
													<template v-for="(unitName, index) in getUnitList" :key="`alert_${index}`">
														<el-form-item
															:prop="`storeStockNums.${index}`"
															class="alert-unit-form-item"
														>
															<div class="alert-unit-group">
																<el-input-number
																	v-model="form.storeStockNums[index]"
																	:min="0"
																	:precision="0"
																	controls-position="right"
																	size="small"
																	class="alert-inline-input"
																	:placeholder="0"
																/>
																<span class="alert-unit-label" :class="{ 'base-unit-text': unitName === unitOptions.find(item => item.id === form.unitId)?.base }">
																	{{ unitName }}
																	<template v-if="unitName === unitOptions.find(item => item.id === form.unitId)?.base">
																		<el-tag size="small" type="primary" class="base-unit-tag-inline">基础</el-tag>
																	</template>
																</span>
															</div>
														</el-form-item>
													</template>
												</div>
											</div>
										</div>
									</el-form-item>
								</el-col>

                <el-col :sm="12" :xs="24">
                    <el-form-item label="排序">
                        <el-input-number v-model="form.sort" :min="0" />
                    </el-form-item>
                </el-col>
                <el-col>
                    <el-form-item label="产品规格" v-if="!route.query.data">
                        <el-radio-group v-model="form.specType" @change="specTypeChange ">
                            <el-radio :label='0'>单规格</el-radio>
                            <el-radio :label='1'>多规格</el-radio>
                        </el-radio-group>
                    </el-form-item>
                </el-col>
                <el-col :sm="12" :xs="24">
                  <el-form-item label="单位比例">
                    <div>{{unitOptions.find(item => item.id === form.unitId)?.mark || '请选择单位'}}</div>
                  </el-form-item>
                </el-col>
                <el-col>
                    <!-- 多规格 -->
                    <div v-if="form.specType === 1"> <el-form-item>
                            <el-select v-model="form.attr" style="width: 240px" placeholder="请选择规格" @change="ruleChange">
                                <el-option v-for=" item  in  ruleList " :label="item.ruleName" :value="item.ruleValue" />
                            </el-select>
                        </el-form-item>
                        <el-form-item v-for=" (item, index)  in  tagList ">
                            <div>
                                <div style="display: flex;"><el-tag effect="dark" closable @close="closeTag(index, null)">{{
                                    item.value
                                }}</el-tag></div>
                                <div style="display: flex;align-items: center;gap: 5px;margin-top: 10px;">
                                    <el-tag closable @close="closeTag(index, tagIndex)"
                                        v-for="(tag, tagIndex) in item.detail">{{ tag }}</el-tag>
                                    <el-input v-model="tagList[index]['addValue']" placeholder="请输入属性名称"
                                        style="width: 200px;">
                                        <template #append><el-button type="primary"
                                                @click="addRuleValue(index, tagList[index]['addValue'])">添加</el-button>
                                        </template></el-input>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item>
                          <div class="flex-center-column">
                            <el-input v-model="tagListItem.attrName" placeholder="请输入规格名称" style="margin-bottom: 10px;width: 100px;"></el-input>
                            <el-input v-model="tagListItem.attrValues" placeholder="请输入属性名称" style="width: 100px;"></el-input>
                            <el-button type="primary" @click="addRules" style="margin-top: 20px;">添加新规格</el-button>
                          </div>
                        </el-form-item>
                        <!-- 批量设置 -->
                        <el-form-item>
                            <el-table :data="batchValue" style="width: 100%;margin-top: 20px;" border>
                                <el-table-column label="图片" align="center" width="100px">
                                    <template #default="scope">
                                        <uploadIcon v-model="scope.row.image" />
                                    </template>
                                </el-table-column>
                                <el-table-column label="库存" align="center">
                                    <template #default="scope">
                                      <div v-for="(item, index) in getUnitList" v-if="getUnitList && getUnitList?.length" :key="item" class="flex-center">
                                        <div class="unit-name">{{item}}</div>
                                        <div v-if="scope.row.unitNums">
                                          <el-input-number v-model="scope.row.unitNums[index]" :min="0" controls-position="right" />
                                        </div>
                                      </div>
                                    </template>
                                </el-table-column>
                                <el-table-column label="重量（KG）" align="center">
                                    <template #default="scope">
                                        <el-input-number v-model="scope.row.weight" :min="0" controls-position="right" />
                                    </template>
                                </el-table-column>
                                <el-table-column label="体积(m³)" align="center">
                                    <template #default="scope">
                                        <el-input-number v-model="scope.row.volume" :min="0" controls-position="right" />
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" align="center">
                                    <template #default="scope">
                                        <el-button type="primary" @click=" handbatch(scope.row)">批量修改</el-button>
                                    </template>
                                </el-table-column>
                            </el-table>
                            <el-table :data="form.attrValue" style="width: 100%;margin-top: 10px;" border>
                                <el-table-column :label="item.value" align="center" v-for=" item  in  tagList">
                                    <template #default="scope">
                                        {{ scope.row[item.value] }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="图片" align="center" width="100px">
                                    <template #default="scope">
                                        <uploadIcon v-model="scope.row.image" />
                                    </template>
                                </el-table-column>
                                <el-table-column label="库存" align="center">
                                  <template #default="scope">
                                    <div v-for="(item, index) in getUnitList" :key="index" class="flex-center">
                                      <div class="unit-name">{{item}}</div>
                                      <el-input-number v-model="scope.row.unitNums[index]" :min="0" controls-position="right" />
                                    </div>
                                  </template>
                                </el-table-column>
                                <el-table-column label="重量（KG）" align="center">
                                    <template #default="scope">
                                        <el-input-number v-model="scope.row.weight" :min="0" controls-position="right" />
                                    </template>
                                </el-table-column>
                                <el-table-column label="体积(m³)" align="center">
                                    <template #default="scope">
                                        <el-input-number v-model="scope.row.volume" :min="0" controls-position="right" />
                                    </template>
                                </el-table-column>
                                <el-table-column label="产品编码" align="center" width="150px">
                                    <template #default="scope">
                                        <el-input v-model="scope.row.attrValueNo" placeholder="请输入产品编码" />
                                    </template>
                                </el-table-column>
                            </el-table>
                        </el-form-item>
                    </div>
                    <!-- 单规格 -->
                    <el-form-item v-if="form.specType === 0">
                        <el-table :data="form.attrValue" style="width: 100%" border>
                            <el-table-column label="图片" align="center" width="100px">
                                <template #default="scope">
                                    <uploadIcon v-model="scope.row.image" />
                                </template>
                            </el-table-column>
                            <el-table-column label="库存" align="center">
                                <template #default="scope">
                                    <div v-for="(item, index) in getUnitList" :key="index" class="flex-center">
                                      <div class="unit-name">{{item}}</div>
                                      <el-input-number v-model="scope.row.unitNums[index]" :min="0" controls-position="right" />
                                    </div>
                                </template>
                            </el-table-column>
                            <el-table-column label="重量（KG）" align="center">
                                <template #default="scope">
                                    <el-input-number v-model="scope.row.weight" :min="0" controls-position="right" />
                                </template>
                            </el-table-column>
                            <el-table-column label="体积(m³)" align="center">
                                <template #default="scope">
                                    <el-input-number v-model="scope.row.volume" :min="0" controls-position="right" />
                                </template>
                            </el-table-column>
                            <el-table-column label="产品编码" align="center" width="150px">
                                <template #default="scope">
                                    <el-input v-model="scope.row.attrValueNo" placeholder="请输入产品编码" />
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                </el-col></el-row>

            <!--<el-row> <el-col :sm="24" :xs="24">
                    <el-form-item label="仓库详情">
                        <tinymce v-model="form.content" style="width: 100%;" />

                    </el-form-item>
                </el-col></el-row>-->
            <el-form-item>
                <el-button type="primary" @click=" onSubmit(ruleFormRef)">确定</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watchEffect, onMounted, computed, toRaw } from 'vue'
import { ElMessage, FormInstance, FormRules } from 'element-plus'
import { saveProduct, getProductRuleList, updateProduct, productInfo } from '@/api/product'
import { treeCategroy } from '@/api/categoryApi'
import { getLogisticsList } from '@/api/logistics'
import { generateBaseData } from '@/utils/dictionary'
import uploadIcon from '@/components/uploadIcon/index.vue'
import tinymce from '@/components/tinymce/index.vue'
import type { LogisticsListRes } from '@/type/logistics.type'
import type { TreeCategroyRes } from '@/type/category.type'
import { useRouter, useRoute } from 'vue-router'
import {unitGroupSaveParams} from "@/type/units.type";
import {getUnitGroupList} from "@/api/units";
const router = useRouter()
const route = useRoute()
const ruleFormRef = ref<FormInstance>()
const form = reactive({
    productName: '',
    cateId: [],
    keyword: '',
    unitId: undefined as number | undefined,
    storeInfo: '',
    image: '',
    sort: 0,
    tempId: '',
    specType: 0,
    attr: '',
    content: '',
    attrValue: [{ unitNums: [], attrValueNo: '' }],
    sliderImage: '',
    isNeedDeliver: true,
    isNew: false,
    isBest: false,
    storeStockNums: [] as number[]
})
const tagListItem = ref({
  attrName: '',
  attrValues: ''
})
// 动态生成警戒值验证规则
const generateStoreStockRules = () => {
  const rules: any = {}
  if (getUnitList.value && getUnitList.value.length > 0) {
    getUnitList.value.forEach((unitName, index) => {
      rules[`storeStockNums.${index}`] = [
        { required: false, message: `请输入${unitName}警戒库存`, trigger: 'blur' },
        { type: 'number', min: 0, message: `${unitName}警戒库存不能小于0`, trigger: 'blur' },
      ]
    })
  }
  return rules
}

const rules = computed<FormRules>(() => ({
    productName: [{ required: true, message: '请输入内容', trigger: 'blur' }],
    cateId: [{ required: true, message: '请选择内容', trigger: 'blur' }],
    // keyword: [{ required: true, message: '请输入内容', trigger: 'blur' }],
    unitId: [{ required: true, message: '请输入内容', trigger: 'blur' }],
    storeInfo: [{ required: true, message: '请输入内容', trigger: 'blur' }],
    image: [{ required: true, message: '请选择内容', trigger: 'blur' }],
    sliderImage: [{ required: true, message: '请选择内容', trigger: 'blur' }],
    tempId: [{ required: true, message: '请选择内容', trigger: 'blur' }],
    type: [{ required: true, message: '请选择内容', trigger: 'blur' }],
    // 动态添加警戒值验证规则
    ...generateStoreStockRules(),
}))
//获取物流模板
const templateList = ref<LogisticsListRes[]>([])
/*getLogisticsList({ page: 1, limit: 10000 }).then(res => {
    templateList.value = res.list
})*/

//仓库分类选择框
const options = ref<TreeCategroyRes[]>([])
const props = { multiple: true, value: 'id', label: 'name', children: 'child' }
treeCategroy({ type: 1, status: -1 }).then(res => [
  options.value = res
])
//单位组选择框
const unitOptions = ref<unitGroupSaveParams[]>([])
getUnitGroupList({page: 1, limit: 10000}).then(res => {
  unitOptions.value = res.list
})
const specTypeChange=() => {
    form.attr=''
    tagList.value=[]
    if (form.specType) {
      batchValue.value[0] = { unitNums: [], attrValueNo: '' }
      form.attrValue = generateBaseData(tagList.value, getUnitList.value?.length);
    } else {
      form.attrValue=[{ unitNums: [], attrValueNo: '' }]
    }
}
//获取规格多选框
let ruleList: any = ref()
function getRuleList() {
    getProductRuleList({ page: 1, limit: 9999 }).then(res => {
        ruleList.value = res.list
    })
}
const getUnitList = computed(() => {
  let list = {} as unitGroupSaveParams;
  if (form.unitId) {
    list = unitOptions.value.find(item => item.id === Number(form.unitId)) as unitGroupSaveParams
  }
  return list.unitNameList as string[];
})
const ruleChange = () => {
    tagList.value = JSON.parse(form.attr)
    form.attrValue = generateBaseData(tagList.value, getUnitList.value?.length);
}
const tagList = ref<any>([])
const closeTag = (index: number, tagIndex: number | null) => {
    if (tagIndex !== null) {
        tagList.value[index]?.detail?.splice(tagIndex, 1)
        if (tagList.value[index].detail.length === 0) {
            tagList.value.splice(index, 1)
        }
    } else {
        tagList.value.splice(index, 1)
    }
    form.attrValue = generateBaseData(tagList.value, getUnitList.value?.length);
}
const addRuleValue = (index: number, str: string) => {
    tagList.value[index]?.detail?.push(str)
    tagList.value[index]['addValue'] = ''
    form.attrValue = generateBaseData(tagList.value, getUnitList.value?.length);
}

/* 批量设置 */
const batchValue = ref([{ unitNums: [] as number[], attrValueNo: '' }])
//点击批量修改
const handbatch = (row: any) => {
    const newRow = row;
    for (const key in newRow) {
        form.attrValue.forEach((item: any) => {
            /* 对没一项数据进行深拷贝，不然每一项数据指向的对象是一样的 */
            item[key] = JSON.parse(JSON.stringify(newRow[key]))
        })
    }
}

getRuleList()
/* 编辑时的回显处理数据 */
onMounted(async () => {
    if (route.query.data) {
        batchValue.value[0] = { unitNums: [], attrValueNo: '' }
        let id = route.query.data
        let data: any = await productInfo(id)
        data.specType = data.specType ? 1 : 0
        data.cateId = data.cateId.split(',').map(Number)

        tagList.value=data.attr.map((item: any)=>{
                item.value=item.attrName
                item.detail=item.attrValues.split(',')
                return item
        })
        data.attr=''
        data.attrValue.forEach((item: any) => {
             let attrValue=  JSON.parse(item.attrValue)
             for (const key in attrValue) {
                item[key]=attrValue[key]
             }
        });

        // 确保警戒值数据在顶层，如果没有则初始化为空数组
        if (!data.storeStockNums) {
            data.storeStockNums = []
        }

        console.log(data);

        Object.assign(form, data)

    }
})

const onSubmit = async (formEl: FormInstance | undefined) => {
    if (!formEl) return
    await formEl.validate((valid, fields) => {
        if (valid) {
            let params = JSON.parse(JSON.stringify(form))
            params.cateId = params.cateId.toString()
            if (params.specType === 0) {
                /* 单规格 */
                params.attr = [{
                    attrName: '规格',
                    attrValues: '默认',
                }]
                params.attrValue[0].attrValue = '{"规格":"默认"}'
                // 确保单规格也有 attrValueNo 字段
                if (!params.attrValue[0].attrValueNo) {
                    params.attrValue[0].attrValueNo = ''
                }
            } else {
                params.attr = tagList.value.map((item: any) => {
                    let obj:any = {
                        attrName: item.value,
                        attrValues: item.detail.toString()
                    }
                    if (route.query.data) {
                        /* 编辑的情况下 */
                        obj.id = item.id
                        obj.productId=params.id
                    }
                    return obj
                })
                params.attrValue.forEach((item: any) => {
                    item.attrValue = {}
                    tagList.value.forEach((tag: any) => {
                        item.attrValue[tag.value] = item[tag.value]
                    });
                    item.attrValue = JSON.stringify(item.attrValue)
                    if (route.query.data) {
                        /* 编辑的情况下 */
                        item.productId = params.id
                    }
                });
            }


            if (route.query.data) {
                updateProduct(params).then(res => {
                    router.go(-1)
                })
            } else {
                saveProduct(params).then(res => {
                    router.go(-1)
                })
            }
        } else {
            ElMessage.warning('请将内容填写完整')
            console.log(fields);
        }
    })
}

const changeUnit = () => {
  if (form.specType === 0) {
    form.attrValue[0] = { unitNums: [], attrValueNo: form.attrValue[0]?.attrValueNo || '' }
  } else {
    form.attrValue = generateBaseData(tagList.value, getUnitList.value?.length);
  }
  // 重置警戒库存数组
  if (getUnitList.value && getUnitList.value.length > 0) {
    form.storeStockNums = new Array(getUnitList.value.length).fill(0)
  }
}

const addRules = () => {
  // 判断规格名称和规格值是否为空
  if (!tagListItem.value.attrName || !tagListItem.value.attrValues) {
    ElMessage.warning('规格名称和规格值不能为空')
    return
  }

  // 判断规格名称是否已存在
  const existingRule = tagList.value.find((item: any) => item.value === tagListItem.value.attrName)
  if (existingRule) {
    ElMessage.warning('规格名称已存在')
    return
  }

  tagList.value.push({
    attrName: tagListItem.value.attrName,
    attrValues: tagListItem.value.attrValues,
    detail: [tagListItem.value.attrValues],
    value: tagListItem.value.attrName
  })
  tagListItem.value = { attrName: '', attrValues: '' }
  form.attrValue = generateBaseData(tagList.value, getUnitList.value?.length);
}
</script>

<style lang="scss" scoped>
.flex-center {
  width: 100%;
  display: flex;
  align-items: center;
}
.flex-center-column {
  display: flex;
  align-items: center;
  flex-direction: column;
}
.unit-name {
  width: 30%;
  font-weight: bold;
  text-align: end;
  margin-right: 10px;
}

/* 警戒库存设置增强样式 */
.alert-stock-section {
  border-left: 4px solid var(--system-page-warning-color);
  background: linear-gradient(135deg,
    var(--system-page-background) 0%,
    var(--el-color-warning-light-9, #fdf6ec) 100%);
  position: relative;
  padding: var(--system-spacing-lg, 16px);
  border-radius: var(--system-spacing-sm, 8px);
  border: 1px solid var(--el-color-warning-light-5, #f0c78a);
}

.alert-stock-section::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  opacity: 0.6;
}

.alert-stock-title .title-with-icon {
  display: flex;
  align-items: center;
  gap: var(--system-spacing-sm, 8px);
  color: var(--el-color-warning-dark-2, #b88230);
  font-weight: 600;
}

.alert-warning-icon {
  color: var(--system-page-warning-color);
  flex-shrink: 0;
}

.alert-stock-desc {
  display: flex;
  align-items: flex-start;
  gap: var(--system-spacing-xs, 4px);
  color: var(--el-color-warning-dark-2, #b88230);
  background: var(--el-color-warning-light-9, #fdf6ec);
  padding: var(--system-spacing-sm, 8px) var(--system-spacing-md, 12px);
  border-radius: var(--system-spacing-xs, 4px);
  border: 1px solid var(--el-color-warning-light-6, #eebe77);
  margin-top: var(--system-spacing-sm, 8px);
}

.desc-info-icon {
  color: var(--system-page-warning-color);
  flex-shrink: 0;
  margin-top: 1px;
}

.alert-input-label {
  color: var(--el-color-warning-dark-2, #b88230);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--system-spacing-xs, 4px);
  white-space: nowrap;
}

.alert-input-number {
  width: 100%;
}

.alert-input-item :deep(.el-input-number) {
  width: 100%;
}

.alert-input-item :deep(.el-input__wrapper) {
  border: 1px solid var(--el-color-warning-light-5, #f0c78a);
  border-radius: var(--system-spacing-xs, 4px);
  transition: all 0.3s ease;
  background: var(--system-page-background);
}

.alert-input-item :deep(.el-input__wrapper:hover) {
  border-color: var(--system-page-warning-color);
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.15);
}

.alert-input-item :deep(.el-input__wrapper.is-focus) {
  border-color: var(--system-page-warning-color);
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
}

.alert-input-item :deep(.el-input__inner::placeholder) {
  color: var(--el-color-warning-light-3, #d4a574);
  font-style: italic;
}

.alert-input-item :deep(.el-input-number__increase),
.alert-input-item :deep(.el-input-number__decrease) {
  border-color: var(--el-color-warning-light-5, #f0c78a);
  color: var(--system-page-warning-color);
}

.alert-input-item :deep(.el-input-number__increase:hover),
.alert-input-item :deep(.el-input-number__decrease:hover) {
  background: var(--el-color-warning-light-8, #faecd8);
  color: var(--el-color-warning-dark-2, #b88230);
}

/* 基础单位样式 */
.base-unit {
  font-weight: 600;
  color: var(--system-primary-color);
}

.base-unit-text {
  color: var(--system-primary-color);
  font-weight: 600;
}

.base-unit-tag-inline {
  margin-left: var(--system-spacing-xs, 4px);
  font-size: 10px;
  height: 16px;
  line-height: 14px;
  padding: 0 4px;
}

/* 单行组合警戒值输入样式 */
.alert-combined-section {
  margin-bottom: var(--system-spacing-lg, 16px);
}

.alert-combined-label {
  margin-bottom: var(--system-spacing-sm, 8px);
}

.alert-combined-label span {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.alert-unit-form-item {
  margin-bottom: 0 !important;
}

.alert-unit-form-item :deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.alert-units-inline {
  display: flex;
  flex-wrap: wrap;
  gap: var(--system-spacing-md, 12px);
  align-items: center;
  padding: var(--system-spacing-sm, 8px);
  background: var(--el-color-warning-light-9, #fdf6ec);
  border: 1px solid var(--el-color-warning-light-5, #f0c78a);
  border-radius: 6px;
  min-height: 48px;
}

.alert-unit-group {
  display: flex;
  align-items: center;
  gap: var(--system-spacing-xs, 4px);
  flex-shrink: 0;
}

.alert-inline-input {
  width: 80px !important;
}

.alert-inline-input :deep(.el-input__wrapper) {
  background: var(--el-color-white);
  border: 1px solid var(--el-color-warning-light-3, #e6a23c);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.alert-inline-input :deep(.el-input__wrapper:hover) {
  border-color: var(--el-color-warning);
  box-shadow: 0 2px 4px rgba(230, 162, 60, 0.2);
}

.alert-inline-input :deep(.el-input__wrapper.is-focus) {
  border-color: var(--el-color-warning);
  box-shadow: 0 0 0 2px rgba(230, 162, 60, 0.2);
}

.alert-unit-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-color-warning-dark-2, #b88230);
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: var(--system-spacing-xs, 4px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .alert-stock-section {
    margin: var(--system-spacing-md, 12px) 0;
    padding: var(--system-spacing-md, 12px);
  }

  .alert-stock-title .title-with-icon {
    gap: var(--system-spacing-xs, 4px);
    font-size: 13px;
  }

  .alert-warning-icon {
    width: 14px;
    height: 14px;
  }

  .alert-stock-desc {
    font-size: 11px;
    padding: var(--system-spacing-xs, 4px) var(--system-spacing-sm, 8px);
    gap: var(--system-spacing-xs, 4px);
  }

  .desc-info-icon {
    width: 12px;
    height: 12px;
  }

  .alert-units-inline {
    flex-direction: column;
    align-items: stretch;
    gap: var(--system-spacing-sm, 8px);
  }

  .alert-unit-group {
    justify-content: space-between;
    padding: var(--system-spacing-xs, 4px) 0;
  }

  .alert-inline-input {
    width: 100px !important;
  }
}

/* 暗色主题适配 */
[data-theme="dark"] .alert-stock-section {
  background: linear-gradient(135deg,
    var(--system-page-background) 0%,
    rgba(245, 158, 11, 0.05) 100%);
  border-left-color: var(--system-page-warning-color);
}

[data-theme="dark"] .alert-stock-section::before {
  border-top-color: rgba(245, 158, 11, 0.3);
}

[data-theme="dark"] .alert-stock-desc {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
  color: #d4a574;
}

[data-theme="dark"] .alert-input-item :deep(.el-input__wrapper) {
  background: var(--system-page-background);
  border-color: rgba(245, 158, 11, 0.4);
}

[data-theme="dark"] .alert-input-item :deep(.el-input__wrapper:hover) {
  border-color: var(--system-page-warning-color);
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.2);
}

[data-theme="dark"] .alert-input-item :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
}

[data-theme="dark"] .alert-units-inline {
  background: rgba(245, 158, 11, 0.1);
  border-color: rgba(245, 158, 11, 0.3);
}

[data-theme="dark"] .alert-inline-input :deep(.el-input__wrapper) {
  background: var(--system-page-background);
  border-color: rgba(245, 158, 11, 0.4);
}

[data-theme="dark"] .alert-inline-input :deep(.el-input__wrapper:hover) {
  border-color: var(--system-page-warning-color);
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);
}

[data-theme="dark"] .alert-inline-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.3);
}

[data-theme="dark"] .alert-unit-label {
  color: #d4a574;
}

[data-theme="dark"] .base-unit-text {
  color: var(--system-primary-color-light);
}
</style>
