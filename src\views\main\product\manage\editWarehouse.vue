<template>
  <div class="layout-container" style="padding: 15px">
    <div style="text-align: left">
      <el-form label-width="80px" :inline="false">
        <el-form-item label-width="0">
          <el-form-item label="商品搜索">
            <div class="layout-container-form-search">
              <!--<el-input v-model="query.keywords" placeholder="请输入关键字" clearable
                        @clear="getTableData(true)"></el-input>-->
              <el-button
                v-if="!dynamicTags.length"
                :icon="Search"
                @click="handleOpen"
              >
                选择商品
              </el-button>
              <el-tag
                closable
                v-for="tag in dynamicTags"
                :key="tag"
                v-if="dynamicTags.length"
                @close="handleClose(tag)"
              >
                商品名称：{{ tag }}
              </el-tag>
              <el-button
                type="primary"
                :icon="Search"
                class="search-btn"
                @click="getTableData(true)"
              >
                {{ $t('message.common.search') }}
              </el-button>
            </div>
          </el-form-item>
        </el-form-item>
        <!--<el-form-item label="商品标识">
            <el-checkbox v-model="query.isHot" label="热卖" />
        </el-form-item>-->
      </el-form>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
        :show-page="false"
      >
        <el-table-column prop="id" label="id" align="center" width="80" />
        <el-table-column label="商品图" align="center">
          <template #default="scope">
            <!-- <img src="" alt="" srcset=""> -->
            <imagePreview :imgurl="scope.row.image"></imagePreview>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" align="center" />
        <el-table-column prop="suk" label="规格" align="center" />
        <el-table-column label="库存" align="center">
          <template #default="scope">
            <div v-for="(item, index) in scope.row.unitNames" class="unit">
              {{ `${item}: ${scope.row.unitNums[index]}` }}
            </div>
          </template>
        </el-table-column>

        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <el-button @click="handleExport(scope.row)">
              {{ $t('message.common.export') }}
            </el-button>
            <el-button @click="handleImport(scope.row)">
              {{ $t('message.common.import') }}
            </el-button>
          </template>
        </el-table-column>
      </Table>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
      <selectPrize
        :layer="selectLayer"
        @getTableData="getSelectPrize"
        v-if="selectLayer.show"
      ></selectPrize>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { defineComponent, ref, reactive, onMounted } from 'vue'
  import { Page } from '@/components/table/type'
  import { LayerInterface } from '@/components/layer/index.vue'
  import { ElMessage } from 'element-plus'
  import {
    deleteProduct,
    restoreProduct,
    getStockList,
    getSkuList,
  } from '@/api/product'
  import Table from '@/components/table/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import { Plus, Delete, Search } from '@element-plus/icons-vue'
  import type { TabsPaneContext } from 'element-plus'
  import type { ProductHeaderRes } from '@/type/product.type'
  import { useRoute } from 'vue-router'
  import Layer from './layer.vue'
  import selectPrize from './selectPrize.vue'
  const route = useRoute()
  const dynamicTags = ref([])
  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
    productId: 0,
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
    width: '40%',
  })

  const selectLayer: LayerInterface = reactive({
    show: false,
    title: '选择商品',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<any[]>([])

  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: any = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getSkuList(query.productId)
      .then(res => {
        let data: any[]
        data = res as any[]
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        // page.total = Number(res.total);
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  // 新增弹窗功能
  const handleExport = (row: any) => {
    layer.title = '出库'
    layer.show = true
    layer.row = row
    layer.type = 2
  }

  const handleImport = (row: any) => {
    layer.title = '入库'
    layer.show = true
    layer.row = row
    layer.type = 1
  }

  const handleClose = (tag: string) => {
    dynamicTags.value.splice((dynamicTags.value as string[]).indexOf(tag), 1)
    query.productId = 0
    getTableData(true)
  }

  const getSelectPrize = (info: any) => {
    query.productId = info.id as number
    ;(dynamicTags.value as string[]).push(info.productName)
    getTableData(true)
  }

  const handleOpen = () => {
    layer.title = '选择商品'
    selectLayer.show = true
  }

  const getTabsHeader = () => {
    /*getTabsHearder().then(res => {
          tabsList.value = res
          getTableData(true)
      })*/
    getTableData(true)
  }
  onMounted(() => {
    if (route.query.data) {
      query.productId = Number(route.query.data) as number
      //获取tab栏
      getTabsHeader()
    }
  })
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }

  .picture {
    width: 30px;
    height: 30px;
  }

  .unit {
    font-weight: bold;
  }
</style>
