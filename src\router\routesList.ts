import { createNameComponent } from './createNode'

/* 首页 */
const dashboard = {
  /* 首页 */
  index: createNameComponent(() => import('@/views/main/dashboard/index.vue')),
}
/** 商品 */
const product = {
  /* 商品管理 */
  manage: createNameComponent(
    () => import('@/views/main/product/manage/index.vue')
  ),
  /* 新增/编辑商品 */
  createproduct: createNameComponent(
    () => import('@/views/main/product/manage/createproduct.vue')
  ),
  /* 仓库管理 */
  editWarehouse: createNameComponent(
    () => import('@/views/main/product/manage/editWarehouse.vue')
  ),
  /* 商品分类 */
  sort: createNameComponent(
    () => import('@/views/main/product/sort/index.vue')
  ),
  /* 商品规格 */
  rule: createNameComponent(
    () => import('@/views/main/product/rule/index.vue')
  ),
  /* 商品评论 */
  reply: createNameComponent(
    () => import('@/views/main/product/reply/index.vue')
  ),
  /* 出入库记录表 */
  warehouseList: createNameComponent(
    () => import('@/views/main/product/manage/warehouseList.vue')
  ),
  /* 产品sku列表 */
  productSkuList: createNameComponent(
    () => import('@/views/main/product/productSkuList/index.vue')
  ),
  /* 产品sku信息 */
  skuSelectList: createNameComponent(
    () => import('@/views/main/product/productSkuList/selectList.vue')
  ),
  /* sku订单信息 */
  skuOrder: createNameComponent(
    () => import('@/views/main/product/productSkuList/skuOrder.vue')
  ),
  /**	产品出库 */
  orderOutbound: createNameComponent(
    () => import('@/views/main/product/orderOutbound/index.vue')
  ),
}
// 单位组管理
const unit = {
  /* 仓库单位管理 */
  product: createNameComponent(
    () => import('@/views/main/unit/product.vue')
	),
	  /* 原料单位管理 */
  marterial: createNameComponent(
    () => import('@/views/main/unit/marterial.vue')
  ),
}

/** 原料 */
const material = {
  /* 原料列表 */
  materialList: createNameComponent(
    () => import('@/views/main/material/materialList.vue')
  ),
  /* 原料分类 */
  materialCate: createNameComponent(
    () => import('@/views/main/material/materialCate.vue')
  ),
  /* 原料出入库记录 */
  materialStockLog: createNameComponent(
    () => import('@/views/main/material/materialStockLog.vue')
  ),
}
/** 订单 */
const order = {
  /* 订单管理 */
  index: createNameComponent(() => import('@/views/main/order/index.vue')),
}
/** 用户 */
const user = {
  /* 用户管理 */
  manager: createNameComponent(
    () => import('@/views/main/user/usermanage/index.vue')
  ),
}
/** 财务 */
const financial = {
  /* 财务操作 */
  operate: {
    /* 申请提现 */
    withdrawals: createNameComponent(
      () => import('@/views/main/financial/operate/withdrawals/index.vue')
    ),
  },
  /* 财务记录 */
  record: {
    /* 充值记录 */
    recharge: createNameComponent(
      () => import('@/views/main/financial/record/recharge/index.vue')
    ),
    /* 余额明细 */
    money: createNameComponent(
      () => import('@/views/main/financial/record/money/index.vue')
    ),
    /* 积分明细 */
    integral: createNameComponent(
      () => import('@/views/main/financial/record/integral/index.vue')
    ),
  },
}
/** 系统设置 */
const systemSetting = {
  /* 系统设置 */
  setting: createNameComponent(
    () => import('@/views/main/systemSetting/setting/index.vue')
  ),
  /* 权限管理 */
  roleManager: {
    /* 角色管理 */
    identityManager: createNameComponent(
      () =>
        import(
          '@/views/main/systemSetting/administratorAuthority/identityManager/index.vue'
        )
    ),
    /* 管理员列表 */
    adminList: createNameComponent(
      () =>
        import(
          '@/views/main/systemSetting/administratorAuthority/adminList/index.vue'
        )
    ),
    /* 权限规则 */
    promiseRules: createNameComponent(
      () =>
        import(
          '@/views/main/systemSetting/administratorAuthority/permissionRules/index.vue'
        )
    ),
  },
  /* 素材管理*/
  picture: createNameComponent(
    () => import('@/views/main/systemSetting/picture/index.vue')
  ),
  /* 物流模板*/
  logistics: createNameComponent(
    () => import('@/views/main/systemSetting/logistics/index.vue')
  ),
}
/** 维护 */
const maintain = {
  /* 开发配置 */
  devconfiguration: {
    /* 配置分类*/
    component: createNameComponent(
      () => import('@/views/main/maintain/devconfig/configcategory/index.vue')
    ),
    /* 组合数据*/
    combineddata: createNameComponent(
      () => import('@/views/main/maintain/devconfig/combineddata/index.vue')
    ),
    /* 表单配置*/
    formConfig: createNameComponent(
      () => import('@/views/main/maintain/devconfig/formconfig/index.vue')
    ),
  },
}
const allRoutes = {
  dashboard,
  product,
  material,
  order,
  user,
  financial,
  systemSetting,
	maintain,
	unit
}
export default allRoutes
