import request from '@/utils/system/request'
import type { ResponseList, ResponseMessage } from '@/type/index.type'
import type {
  MaterialListParams,
  MaterialListRes,
  MaterialStockLogParams,
  MaterialStockLogRes,
} from '@/type/material.type'

/**
 * 获取原料列表
 * @param data
 */
export function getMaterialList(
  data: MaterialListParams
): Promise<ResponseList<MaterialListRes[]>> {
  return request({
    url: '/api/raw/material/list',
    method: 'get',
    params: data,
  })
}

/**
 * 删除原料
 * @param id
 */
export function deleteMaterial(id: number): Promise<ResponseMessage> {
  return request({
    url: `/api/raw/material/delete/${id}`,
    method: 'get',
  })
}

/**
 * 编辑原料
 * @param data
 */
export function updateMaterial(data: any): Promise<ResponseMessage> {
  return request({
    url: '/api/raw/material/update',
    method: 'post',
    data: data,
  })
}

/**
 * 新增原料
 * @param data
 */
export function saveMaterial(data: any): Promise<ResponseMessage> {
  return request({
    url: '/api/raw/material/save',
    method: 'post',
    data: data,
  })
}

/**
 * 原料出入库
 * @param data
 */
export function changeMaterialStock(data: {
  rawMaterialId: number
  type: number
  num?: number // 兼容性保留
  nums?: number[] // 多单位支持
}): Promise<ResponseMessage> {
  return request({
    url: '/api/raw/material/change/stock',
    method: 'post',
    data: data,
  })
}

/**
 * 获取原料出入库记录列表
 * @param data
 */
export function getMaterialStockLogList(
  data: MaterialStockLogParams
): Promise<ResponseList<MaterialStockLogRes[]>> {
  return request({
    url: '/api/raw/material/stockLog/list',
    method: 'get',
    params: data,
  })
}

/**
 * 导出原料出入库记录Excel
 * @param data
 */
export function exportMaterialStockLog(data: {
  rawMaterialId: number
  type: number
  dateLimit: string
}): Promise<ResponseMessage> {
  return request({
    url: '/api/raw/material/stockLog/excelOutput',
    method: 'post',
    data: data,
  })
}
