import request from '@/utils/system/request'
import type { ResponseList, ResponseMessage } from '@/type/index.type'
import type {
  StatusNumParams,
  StatusNumRes,
  OrderListParams,
  OrderListRes,
  RefundOrderParams,
  refuseOrderParams,
  ExcelPurchaseOrderParams,
  IntelligencePurchaseOrderParams,
  IntelligencePurchaseOrderResponse,
  CopyPurchaseOrderResponse,
} from '@/type/order.type'
import { AddPurchaseOrderVo } from '@/type/order.type'
// 订单状态数量
export function getStatusNum(data: StatusNumParams): Promise<StatusNumRes> {
  return request({
    url: '/api/admin/store/order/status/num',
    method: 'get',
    params: data,
  })
}

//订单列表
export function getOrderList(
  data: OrderListParams
): Promise<ResponseList<OrderListRes[]>> {
  return request({
    url: '/api/admin/store/order/list',
    method: 'get',
    params: data,
  })
}

//订单详情
export function orderInfo(data: { orderNum: string }) {
  return request({
    url: '/api/admin/store/order/info',
    method: 'get',
    params: data,
  })
}

// 生成采购单
export function addPurchaseOrder(data: AddPurchaseOrderVo) {
  return request({
    url: '/api/admin/stockOrder/addPurchaseOrder',
    method: 'post',
    data: data,
  })
}

//出入库订单列表
export function getStockOrderList(data: any): Promise<ResponseList<any[]>> {
  return request({
    url: '/api/admin/stockOrder/list',
    method: 'get',
    params: data,
  })
}

// 获取采购单详情
export function purchaseOrderInfo(id: number) {
  return request({
    url: `/api/admin/stockOrder/purchaseOrderInfo/${id}`,
    method: 'get',
  })
}

// 导出采购订单Excel
export function excelPurchaseOrder(
  data: ExcelPurchaseOrderParams
): Promise<ResponseMessage> {
  return request({
    url: '/api/admin/stockOrder/excelPurchaseOrder',
    method: 'post',
    data: data,
  })
}

// 智能采购单
export function intelligencePurchaseOrder(
  data: IntelligencePurchaseOrderParams
): Promise<IntelligencePurchaseOrderResponse> {
  return request({
    url: '/api/admin/stockOrder/intelligencePurchaseOrder',
    method: 'post',
    data: data,
  })
}

// 复制采购订单
export function copyPurchaseOrder(id: number): Promise<ResponseMessage<CopyPurchaseOrderResponse>> {
  return request({
    url: `/api/admin/stockOrder/copyPurchaseOrder/${id}`,
    method: 'get',
  })
}
