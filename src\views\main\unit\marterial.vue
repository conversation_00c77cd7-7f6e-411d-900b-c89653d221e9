<template>
  <div class="layout-container">
    <div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ $t('message.common.add') }}
        </el-button>
        <el-popconfirm
          :title="$t('message.common.delTip')"
          @confirm="handleDel(chooseData)"
        >
          <template #reference>
            <el-button
              type="danger"
              :icon="Delete"
              :disabled="chooseData.length === 0"
            >
              {{ $t('message.common.delBat') }}
            </el-button>
          </template>
        </el-popconfirm>
      </div>
      <div class="layout-container-form-search">
        <el-input
          v-model="query.keywords"
          :placeholder="$t('message.common.searchTip')"
          @change="getTableData(true)"
        ></el-input>
        <el-button
          type="primary"
          :icon="Search"
          class="search-btn"
          @click="getTableData(true)"
        >
          {{ $t('message.common.search') }}
        </el-button>
      </div>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :showSelection="true"
        :data="tableData"
        @getTableData="getTableData"
        @selection-change="handleSelectionChange"
      >
        <el-table-column prop="id" label="id" align="center" />
        <el-table-column prop="groupName" label="单位组名称" align="center" />
        <el-table-column prop="unitNames" label="单位组元素" align="center" />
        <el-table-column prop="scales" label="单位组比例值" align="center" />
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)">
              {{ $t('message.common.update') }}
            </el-button>
            <el-popconfirm
              :title="$t('message.common.delTip')"
              @confirm="handleDel([scope.row])"
            >
              <template #reference>
                <el-button type="danger">
                  {{ $t('message.common.del') }}
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </Table>
      <Layer :layer="layer" :type="2" @getTableData="getTableData" v-if="layer.show" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import Table from '@/components/table/index.vue'
  import Layer from './unitComposition.vue'
  import { Plus, Search, Delete } from '@element-plus/icons-vue'
  import { useUnitController } from './useController'

  // 使用原料单位控制器 (type: 2)
  const {
    query,
    layer,
    page,
    loading,
    tableData,
    chooseData,
    handleSelectionChange,
    getTableData,
    handleDel,
    handleAdd,
    handleEdit,
    initData,
  } = useUnitController(2)

  // 初始化数据
  initData()
</script>

<style lang="scss" scoped></style>