/***@name 登录请求体  */
export interface LoginApiParams {
  /**
   *@name 后台管理员账号
   */
  account: string
  /**
   *@name 验证码
   */
  code: string
  /**
   *@name 密钥
   */
  key: string
  /**
   *@name 后台管理员密码
   */
  pwd: string
}
/***@name 登录响应体  */
export interface LoginApiRes {
  /**
   *@name 后台管理员账号
   */
  account: string
  /**
   *@name 后台管理员账号
   */
  id: number
  /**
   *@name 是否接收状态
   */
  isSms: boolean

  /**
   *@name 真实姓名
   */
  realName: string
  /**
   *@name token
   */
  token: string
}
/***@name 管理员信息响应体  */
export interface GetInfoApiRes {
  account?: string
  addTime?: number
  id?: number
  /**
   * 是否接收短信
   */
  isSms?: boolean
  lastIp?: string
  lastTime?: Date
  level?: number
  loginCount?: number
  /**
   * 权限标识数组
   */
  permissionsList?: string[]
  /**
   * 手机号码
   */
  phone?: string
  realName?: string
  roleNames?: string
  roles?: string
  status?: boolean
}

/*** @name 获取验证码响应体 */
export interface GetCodeRes {
  /**
   *@name 验证码图片
   */
  code: string
  /**
   *@name 密钥
   */
  key: string
}
/*** @name 获取路由菜单响应体 */
export interface GetMenuRes {
  /**
   *@name 子对象列表
   */
  childList?: GetCodeRes[]
  /**
   *@name 组件路径
   */
  component?: string
  /**
   *@name icon
   */
  icon?: string
  /**
   *@name ID
   */
  id?: number
  /**
   *@name 类型，M-目录，C-菜单，A-按钮
   */
  menuType?: string
  /**
   *@name 名称
   */
  name?: string
  /**
   *@name 权限标识
   */
  perms?: string
  /**
   *@name 父级ID
   */
  pid?: number
  /**
   *@name 排序
   */
  sort?: number
}
