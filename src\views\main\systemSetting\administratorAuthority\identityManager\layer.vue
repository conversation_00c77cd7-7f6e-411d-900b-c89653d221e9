<template>
  <Layer :layer="layer" @confirm="submit(ruleForm)" ref="layerDom">
    <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" style="margin-right:30px;">
      <el-form-item label="角色昵称：" prop="roleName">
        <el-input v-model="form.roleName" placeholder="请输入角色昵称："></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-switch v-model="form.status" active-color="#13ce66" inactive-color="#ff4949" :active-value="true"
          :inactive-value="false"></el-switch>
      </el-form-item>
      <el-form-item label="权限">
        <el-tree ref="treeRef" :data="rulesList" show-checkbox node-key="id" :props="defaultProps" v-show="treeShow" />
      </el-form-item>
    </el-form>
  </Layer>
</template>

<script lang="ts" setup>
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import type { AddRoleParams, MenuListRes } from '@/type/systemSetting.type'
import { getMenuList, addRole, getRoleInfo, updateRole } from '@/api/systemSetting'
import { defineEmits, ref, defineProps } from 'vue'
import Layer from '@/components/layer/index.vue'
import { ElTree } from 'element-plus'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import {getMenus} from "@/api/user";

const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})
const emit = defineEmits(['getTableData'])
const treeRef = ref<InstanceType<typeof ElTree>>()
const ruleForm = ref<FormInstance>()
const layerDom: Ref<LayerType | null> = ref(null)
let form = ref({
  id: 0,
  roleName: '',
  status: false,
  rules: []
})
const rulesList = ref<any>([])
const rules = {
  roleName: [{ required: true, message: '请输入用户昵称', trigger: 'blur' }],

}
const defaultCheck = ref<MenuListRes[]>([])
function getMenu() {
  getMenus().then((res) => {
    let data = res
    data.forEach((d) => {
      d.loading = false
    })
    let obj: any = {
    }
    //把子菜单退进父级
    data.forEach((item: any) => {
      obj = item;
      obj.children = [];
      data.map((element:MenuListRes, index: number) => {
        if (item.id == element.pid) {
          obj.children.push(element)
        }
      });
      if (obj.menuType != 'C' && obj.pid == 0) {
        rulesList.value.push(obj);
      }
    })
  })
}

const treeData = ref([])
const defaultProps = {
  children: 'childList',
  label: 'name',
}
const treeShow = ref(false)
const menuList = ref()

function getCheckList(list: Array<any>) {
  for (const key in list) {
    if (list[key].checked && list[key].childList.length == 0) {
      defaultCheck.value.push(list[key].id)
    } else { }
    if (list[key].childList.length > 0) {
      getCheckList(list[key].childList)
    }

  }
}
function init() { // 用于判断新增还是编辑功能
  getMenu()
  if (props.layer.row) {
    form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
    getRoleInfo(form.value.id).then(res => {
      menuList.value = res.menuList
      getCheckList(menuList.value)

      setTimeout(() => {
        treeShow.value = true
        treeRef.value!.setCheckedKeys(defaultCheck.value, false)
      }, 100);
    })
  } else {
    treeShow.value = true
  }
}
init()

const submit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {

    if (valid) {
      let checkedKeys: any = treeRef.value!.getCheckedKeys();
      let halfCheckedKeys = treeRef.value!.getHalfCheckedKeys();
      checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
      checkedKeys = checkedKeys.toString()

      let params = form.value
      params.rules = checkedKeys
      if (props.layer.row) {
        updateForm(params)
      } else {
        addForm(params)
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}

// 新增提交事件
function addForm(params: AddRoleParams) {

  addRole(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '新增成功'
      })
      emit('getTableData', true)
      layerDom.value && layerDom.value.close()
    })
}

// 编辑提交事件
function updateForm(params: AddRoleParams) {
  updateRole(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '编辑成功'
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
}





</script>

<style lang="scss" scoped></style>