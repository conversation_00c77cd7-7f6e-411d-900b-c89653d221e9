<template>
  <div class="layout-container" style="padding: 15px">
    <el-tabs v-model="query.type" class="demo-tabs" @tab-click="clickTab">
      <el-tab-pane
        :label="item.name + `(${item.count})`"
        :name="item.type"
        v-for="item in tabsList"
        :key="item.type"
      ></el-tab-pane>
    </el-tabs>
    <div style="text-align: left">
      <el-form label-width="80px" :inline="false">
        <el-form-item label="时间">
          <timePicker
            v-model="query.dateLimit"
            @change="getTableData(true)"
          ></timePicker>
        </el-form-item>
        <el-form-item label-width="0">
          <el-form-item label="订单搜索">
            <div class="layout-container-form-search">
              <el-input
                v-model="query.keywords"
                placeholder="关键字(订单号或手机号)"
                clearable
                @clear="getTableData(true)"
              ></el-input>
            </div>
          </el-form-item>
          <el-form-item label="操作类型">
            <el-select
              v-model="query.type"
              clearable
              @change="getTableData(true)"
              class="inputWidth"
            >
              <el-option label="入库" :value="1" />
              <el-option label="出库" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button
              style="margin-left: 10px"
              type="primary"
              :icon="Search"
              class="search-btn"
              @click="getTableData(true)"
            >
              {{ $t('message.common.search') }}
            </el-button>
          </el-form-item>
        </el-form-item>
        <!--<el-form-item label="商品标识">
            <el-checkbox v-model="query.isHot" label="热卖" />
        </el-form-item>-->
      </el-form>
    </div>
		<div class="layout-container-form flex space-between">
			<el-button
        type="success"
        :icon="Download"
        :loading="exportLoading"
        @click="handleStatisticsExport"
      >
        发货统计导出
      </el-button>
		</div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column prop="id" label="id" align="center" width="80" />
        <el-table-column prop="orderNo" label="订单号" align="center" />
        <el-table-column prop="phone" label="手机号" align="center" />
        <el-table-column prop="customerName" label="客户名称" align="center" />
        <el-table-column label="类型" align="center">
          <template #default="scope">
            {{ scope.row.type === 1 ? '入库' : '出库' }}
          </template>
        </el-table-column>
        <el-table-column prop="mark" label="备注" align="center" />
        <el-table-column prop="totalAmount" label="金额" align="center">
          <template #default="scope">
            <span v-if="scope.row.totalAmount !== null && scope.row.totalAmount !== undefined">
              ¥{{ formatPrice(scope.row.totalAmount) }}
            </span>
            <span v-else class="text-gray-400">--</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column prop="createBy" label="操作人" align="center" />
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          width="350"
        >
          <template #default="scope">
            <el-button @click="handleSelect(scope.row)">
              {{ $t('message.common.select') }}
            </el-button>
						<template v-if="scope.row.type != 1">
							<el-button
            	  type="warning"
            	  @click="handleCopy(scope.row)"
            	  :loading="scope.row.copyLoading"
            	>
            	  复制订单
            	</el-button>
							<el-button
            	  type="success"
            	  @click="handleExport(scope.row)"
            	  :loading="scope.row.exportLoading"
            	>
            	  {{ $t('message.common.exportExcel') }}
            	</el-button>
            	<el-button
            	  type="primary"
            	  @click="handlePrint(scope.row)"
            	  :loading="scope.row.printLoading"
            	  :icon="Printer"
            	>
            	  打印
            	</el-button>
						</template>
          </template>
        </el-table-column>
      </Table>
      <Layer
        v-model:show="layer.show"
        :title="layer.title"
        :row="layer.row"
        @getTableData="getTableData(true)"
      ></Layer>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { defineComponent, ref, reactive, watch } from 'vue'
  import { Page } from '@/components/table/type'
  import { LayerInterface } from '@/components/layer/index.vue'
  import Layer from './layer.vue'
  import timePicker from '@/components/timePicker/index.vue'
  import { ElMessage, ElMessageBox } from 'element-plus'
  import { treeCategroy } from '@/api/categoryApi'
  import Table from '@/components/table/index.vue'
  import { safePrintPDFHighQuality } from '@/utils/safePrint'
  import { Plus, Delete, Search, Printer, Download } from '@element-plus/icons-vue'

  import type { TabsPaneContext } from 'element-plus'
  import type { ProductHeaderRes } from '@/type/product.type'
  import { useRouter } from 'vue-router'
  import { TreeCategroyRes } from '@/type/category.type'
  import type { ProductListParams, ProductListRes } from '@/type/product.type'
  import { getStockOrderList, excelPurchaseOrder, copyPurchaseOrder } from '@/api/order'
  import { RuleListRes } from '@/type/product.type'
  import { downloadExcelPost } from '@/utils/downloadExcel'
  import store from '@/store'
  import { exportDataWithLoading, createExportConfig, exportDebounce } from '@/utils/exportUtils'
  const router = useRouter()

  // 格式化价格显示的函数
  const formatPrice = (price: number | string | undefined | null) => {
    if (price === null || price === undefined || price === '') {
      return '0.00'
    }
    const numPrice = typeof price === 'string' ? parseFloat(price) : price
    if (isNaN(numPrice)) {
      return '0.00'
    }
    return numPrice.toFixed(2)
  }

  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
    type: null, // 默认为入库类型
    dateLimit: '',
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增商品',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<ProductListRes[]>([])
  const exportLoading = ref(false)

  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: ProductListParams = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getStockOrderList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
          d.exportLoading = false
          d.printLoading = false
          d.copyLoading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }

  const handleSelect = (row: RuleListRes) => {
    layer.title = '查看订单'
    layer.row = row
    layer.show = true
    layer.showButton = false
  }

  const tabsList = ref<ProductHeaderRes[]>([])

  //切换tab
  const clickTab = (tab: TabsPaneContext, event: Event) => {
    getTableData(true)
  }
  //商品分类选择框
  const options = ref<TreeCategroyRes[]>([])
  const props = {
    value: 'id',
    label: 'name',
    children: 'child',
    emitPath: false,
  }
  treeCategroy({ type: 1, status: -1 }).then(res => [(options.value = res)])

  //恢复商品
  const getTabsHeader = () => {
    /*getTabsHearder().then(res => {
          tabsList.value = res
          getTableData(true)
      })*/
    getTableData(true)
  }

  // 创建防抖映射，为每个订单ID创建独立的防抖函数
  const exportDebounceMap = new Map()

  // 获取或创建防抖函数
  const getExportHandler = (orderId: string) => {
    if (!exportDebounceMap.has(orderId)) {
      const debouncedHandler = exportDebounce(async (row: any) => {
        // 创建响应式的loading引用
        const loadingRef = { value: row.exportLoading }
        
        // 使用通用导出函数
        const config = createExportConfig.purchaseOrder([row.id], row.orderNo)
        
        // 同步loading状态
        row.exportLoading = true
        try {
          await exportDataWithLoading(config, loadingRef)
        } finally {
          row.exportLoading = loadingRef.value
        }
      }, { delay: 500, minInterval: 1000 })
      exportDebounceMap.set(orderId, debouncedHandler)
    }
    return exportDebounceMap.get(orderId)
  }

  // 修改handleExport函数，使用防抖处理
  const handleExport = async (row: any) => {
    const handler = getExportHandler(row.id)
    handler(row)
  }

  // 打印功能 - 使用安全打印工具修复Edge浏览器问题
  const handlePrint = async (row: any) => {
    row.printLoading = true
    try {
      const baseURL = import.meta.env.VITE_DOWNLOAD_URL
      const url = baseURL + '/api/admin/stockOrder/pdfPurchaseOrder'

      // 使用axios获取PDF文件流
      const { default: axios } = await import('axios')
      const res = await axios({
        method: 'post',
        url: url,
        responseType: 'blob',
        data: { orderIds: [row.id] },
        headers: {
          Authorization: 'Bearer ' + store.getters['user/token'],
          adminToken: store.getters['user/token'],
          'Content-Type': 'application/json',
        },
      })

      // 检查返回的数据类型
      if (res.data.type === 'application/json') {
        const fileReader = new FileReader()
        fileReader.readAsText(res.data, 'utf-8')
        fileReader.onload = function () {
          let msg = JSON.parse(fileReader.result as string).message
          ElMessage.error(msg)
        }
        return
      }

      // 创建PDF blob并使用安全打印
      const pdfBlob = new Blob([res.data], { type: 'application/pdf' })

      // 使用高质量模式进行PDF打印
      await safePrintPDFHighQuality(pdfBlob, {
        showModal: true,         // 显示打印加载提示
        documentTitle: '订单打印',
        showActivationTip: true,
        printJSOptions: {
          // print-js 的额外配置
          modalMessage: '正在准备高质量打印订单...',
          fallbackPrintable: '<p>打印失败，请重试</p>'
        }
      })

    } catch (e: any) {
      console.log('打印失败', e)
      if (e.message?.includes('点击页面')) {
        ElMessage.warning(e.message)
      } else {
        ElMessage.error('打印失败: ' + (e.message || e))
      }
    } finally {
      row.printLoading = false
    }
  }

  //获取tab栏
  // 复制订单功能
  const handleCopy = (row: any) => {
    ElMessageBox.confirm(
      `确定要复制订单号为 "${row.orderNo}" 的订单吗？复制后将跳转到出库页面。`,
      '复制订单确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(() => {
      // 用户确认后执行复制操作
      performCopyOrder(row)
    }).catch(() => {
      // 用户取消，不做任何操作
    })
  }

  // 执行复制订单操作
  const performCopyOrder = async (row: any) => {
    row.copyLoading = true
    try {
      const response = await copyPurchaseOrder(row.id)
			console.log('res', response);

      if (response) {
        ElMessage.success('订单复制成功，正在跳转到出库页面...')

        // 将复制的数据存储到sessionStorage中，供出库页面使用
        const copyData = {
          phone: response.phone || '',
          mark: response.mark || '',
          type: response.type,
          skuList: response.skuList || [],
          // 新增字段支持
          customerName: response.customerName || '',
          examineApprove: response.examineApprove || '',
          examine: response.examine || '',
          documentPreparation: response.documentPreparation || '',
          consignee: response.consignee || '',
          driver: response.driver || '',
          // 赠品列表支持
          giftList: response.giftList || []
        }

        sessionStorage.setItem('copyOrderData', JSON.stringify(copyData))

        // 跳转到出库页面
        setTimeout(() => {
          router.push('/product/orderOutbound')
        }, 1000)
      } else {
        ElMessage.error(response.message || '复制订单失败')
      }
    } catch (error: any) {
      console.error('复制订单失败:', error)
      ElMessage.error(error.message || '复制订单失败')
    } finally {
      row.copyLoading = false
    }
  }

  // 发货统计导出功能
  const originalHandleStatisticsExport = async () => {
    // 准备导出参数，只传有值的字段
    const exportParams: any = {}

    if (query.keywords && query.keywords.trim() !== '') {
      exportParams.keywords = query.keywords.trim()
    }

    if (query.type !== null && query.type !== undefined) {
      exportParams.type = query.type
    }

    if (query.dateLimit && query.dateLimit.trim() !== '') {
      exportParams.dateLimit = query.dateLimit.trim()
    }

    // 使用通用导出函数
    const config = createExportConfig.orderStatistics(exportParams)
    await exportDataWithLoading(config, exportLoading)
  }

  // 使用防抖包装的导出函数 - 500ms防抖，1秒最小间隔
  const handleStatisticsExport = exportDebounce(originalHandleStatisticsExport, { delay: 500, minInterval: 1000 })

  // 监听弹窗关闭，清理状态
  watch(
    () => layer.show,
    (newShow, oldShow) => {
      // 当弹窗从显示变为隐藏时，清理相关状态
      if (oldShow === true && newShow === false) {
        // 清理弹窗相关状态，避免状态污染
        layer.row = undefined
        layer.title = ''
        layer.showButton = true
      }
    }
  )

  getTabsHeader()
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }

  .picture {
    width: 30px;
    height: 30px;
  }
</style>
