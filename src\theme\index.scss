:root {
  // 主题色
  --system-primary-color: #409eff; // 可做背景色和文本色，用做背景色时，需要和--system-primary-text-color配合使用，避免文件颜色和主题色冲突
  --system-primary-text-color: #fff; // 主题色作为背景色时使用

  // logo颜色相关
  --system-logo-color: #f1f1f1;
  --system-logo-background: #263445;

  // 菜单颜色相关
  --system-menu-text-color: #bfcbd9;
  --system-menu-background: #181f31;
  --system-menu-children-background: #1f2d3d;
  --system-menu-submenu-active-color: #fff; 
  --system-menu-hover-background: #203448;

  // header区域
  --system-header-background: #fff;
  --system-header-text-color: #303133;
  --system-header-breadcrumb-text-color: #606266;
  --system-header-item-hover-color: rgba(0,0,0,.06);
  --system-header-border-color: #d8dce5;
  --system-header-tab-background: #fff;

  // contaier区域，父框架
  --system-container-background: #f0f2f5;
  --system-container-main-background: #fff;

  // 页面区域, 这一块是你在自己写的文件中使用主题，核心需要关注的地方
  --system-page-background: #fff; // 主背景
  --system-page-color: #303133; // 主要的文本颜色
  --system-page-tip-color: rgba(0, 0, 0, 0.45); // 协助展示的文本颜色
  --system-page-border-color: #ebeef5; // 通用的边框配置色，便于主题扩展

  // 扩展的页面样式变量
  --system-page-secondary-color: #606266; // 次要文本颜色
  --system-page-muted-color: #909399; // 静音/禁用文本颜色
  --system-page-success-color: #67c23a; // 成功状态颜色
  --system-page-warning-color: #e6a23c; // 警告状态颜色
  --system-page-danger-color: #f56c6c; // 危险状态颜色
  --system-page-info-color: #909399; // 信息状态颜色

  // 卡片和容器样式
  --system-card-background: #fff; // 卡片背景色
  --system-card-border-color: #ebeef5; // 卡片边框色
  --system-card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); // 卡片阴影
  --system-card-radius: 8px; // 卡片圆角

  // 间距系统
  --system-spacing-xs: 4px;
  --system-spacing-sm: 8px;
  --system-spacing-md: 12px;
  --system-spacing-lg: 16px;
  --system-spacing-xl: 24px;
  --system-spacing-xxl: 32px;

  // 标题和强调样式
  --system-title-border-color: var(--system-primary-color); // 标题左侧边框色
  --system-title-border-width: 4px; // 标题左侧边框宽度
  
  // element主题色修改
  --el-color-primary: var(--system-primary-color);
}

// 进度条颜色修改为主题色
body #nprogress .bar {
  background-color: var(--system-primary-color);
}
body #nprogress .peg {
  box-shadow: 0 0 10px var(--system-primary-color), 0 0 5px var(--system-primary-color);
}
body #nprogress .spinner-icon {
  border-top-color: var(--system-primary-color);
  border-left-color: var(--system-primary-color);
}

@import './modules/dark.scss';