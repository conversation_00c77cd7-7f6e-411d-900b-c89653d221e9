import {
  defineConfig,
  presetAttributify,
  presetIcons,
  presetWind,
} from 'unocss'

export default defineConfig({
  presets: [
    presetWind(), // 提供 Tailwind CSS / Windi CSS 兼容的工具类
    presetAttributify(), // 提供属性化模式
    presetIcons(), // 提供图标支持
  ],
  shortcuts: [
    // 自定义快捷方式
    [
      'btn',
      'px-4 py-1 rounded inline-block bg-teal-600 text-white cursor-pointer hover:bg-teal-700 disabled:cursor-default disabled:bg-gray-600 disabled:opacity-50',
    ],
    [
      'icon-btn',
      'text-[0.9em] inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-teal-600',
    ],
  ],
  rules: [
    // 自定义规则
    [/^animate-fade-in-down$/, () => ({
      'animation': 'fadeInDown 0.3s ease-out'
    })],
  ],
  theme: {
    animation: {
      keyframes: {
        fadeInDown: `{
          0% { opacity: 0; transform: translateY(-10px); }
          100% { opacity: 1; transform: translateY(0); }
        }`
      }
    }
  },
})
