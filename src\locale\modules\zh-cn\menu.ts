/*
 * @Date: 2022-05-22 20:44:25
 * @Description: 
 */
export default {
  menu: {
    dashboard: {
      name: 'dashboard',
      index: '首页'
    },
    system: {
      name: '系统目录',
      redirect: '重定向页面',
      '404': '404',
      '401': '401'
    },
    component: {
      name: '组件',
      button: '按钮',
      wordEditor: '文本编辑器',
      mdEditor: 'md编辑器',
      codeEditor: '代码编辑器',
      jsonEditor: 'JSON编辑器',
      dragPane: '可拖拽面板',
      map: '地图组件',
      cutPhoto: '弹窗图片裁剪',
      rightMenu: '右键菜单',
      exportExcel:'导出文件'
    },
    page: {
      name: '页面',
      crudTable: '业务表格',
      categoryTable: '分类联动表格',
      treeTable: '树联动表格',
      card: '卡片列表',
      work: '工作进度',
      baidu: '外部链接',
    },   
    menu: {
      name: '多级嵌套菜单',
      menu_1: '二级菜单1',
      menu_1_1: '三级菜单1-1',
      menu_1_1_1: '四级菜单1-1-1',
      menu_1_1_2: '四级菜单1-1-2',
      menu_1_2: '三级菜单1-2',
      menu_2: '二级菜单2',
      menu_3: '二级菜单3'
    },
    directive: {
      name: '自定义指令',
      dragable: '拖拽指令：v-dragable',
      copy: '复制指令：v-copy',
      waterMarker: '水印指令：v-waterMarker',
      longpress: '长按指令：v-longpress',
      debounce: '按钮防抖指令：v-debounce',
      scroll: '下拉加载指令：v-infinite-scroll',
      clickOutside: '点击外部区域：v-click-outside',
    },
    echarts: {
      name: 'echarts图表',
      bar: '柱状图',
      line: '折线图',
      pie: '饼图',
      radar: '雷达图',
      map: '地图',
    },
    systemManage: {
      name: '系统管理',
      menu: '菜单',
      role: '角色',
      user: '用户'
    },
    print: {
      name: '打印功能',
      jsPrint: 'JS打印事件'
    },
    community: {
      name: '社区的力量',
      qq: 'QQ交流群',
      site: 'vue3最新资源库'
    },
    order:{
      orderindex:'订单管理',
    },
    document: {
      name: '文档',
      intro: '使用说明',
      function: '功能说明',
      menu: '路由菜单配置',
      keepAlive: 'keepAlive使用说明',
      crud: '数据表格的增删改查',
      theme: '自定义主题',
      systemfont: '项目图标的说明',
      api: '接口说明文档'
    },
    tab: {
      name: 'tab事件公用',
    },
  },
}