import request from '@/utils/system/request'
import type {UpdateFinanceParams,UserListRes,UserListParams,UserInfoParams} from '@/type/userManage.type'
import type {ResponseList,ResponseMessage} from '@/type/index.type'
// 获取用户列表
export function getUserList(data: UserListParams):Promise<ResponseList<UserListRes[]>> { return request({
    url: '/api/admin/user/list',
    method: 'get',
    params: data
  })
}
//修改用户余额
export function updateFinance(data:UpdateFinanceParams):Promise<ResponseMessage> {
  return request({
    url: '/api/admin/user/updateFinance',
    method: 'post',
    data: data
  })
}
/**@name  根据id查询用户信息 */
export function getUserInfo(data: UserInfoParams):Promise<UserListRes> {
  return request({
    url: "/api/admin/user/info",
    method: "get",
    params: data,
  })
}
