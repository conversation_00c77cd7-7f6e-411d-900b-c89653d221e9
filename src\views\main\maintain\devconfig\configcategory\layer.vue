<template>
  <Layer :layer="props.layer" @confirm="submit(ruleForm)" ref="layerDom">
    <el-form :model="form" :rules="rules" ref="ruleForm" label-width="120px" style="margin-right:30px;">
      <el-form-item label="父级">
        <el-cascader :options="options" :show-all-levels="false" v-model="form.pid"
          :props="{ label: 'name', value: 'id', emitPath: false }" />
      </el-form-item>
      <el-form-item label="分类名称">
        <el-input v-model="form.name" placeholder="请输入内容"></el-input>
      </el-form-item>
      <el-form-item label="英文名称">
        <el-input v-model="form.url" placeholder="请输入内容"></el-input>
      </el-form-item>
      <el-form-item label="排序" prop="phone">
        <el-input-number v-model="form.sort" :min="1" />
      </el-form-item>
      <el-form-item label="状态">
        <el-switch v-model="form.status" :active-value="true" :inactive-value="false" />
      </el-form-item>
    </el-form>
  </Layer>
</template>

<script lang="ts" setup>
import { treeCategroy, addCategroy, updateCategroy } from '@/api/categoryApi'
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref } from 'vue'
import { defineComponent, ref } from 'vue'
import { ElMessage } from 'element-plus'
import Layer from '@/components/layer/index.vue'
import type { CategroyParms,  updateCategroyParms } from '@/type/category.type'
import type { FormInstance, FormRules } from 'element-plus'
const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})
const emit = defineEmits(['getTableData'])
const ruleForm = ref<FormInstance>()
const layerDom: Ref<LayerType | null> = ref(null)
let form = ref({
  id: 0,
  status: false,
  sort: 1,
  name: '',
  pid: 0,
  url: '',
  type: 6
})
const options = ref()
const rules = {
  name: [{ required: true, message: '请输入账号', trigger: 'blur' }],

}
init()
function init() { // 用于判断新增还是编辑功能
  treeCategroy({ type: 6, status: -1 }).then(res => {
    options.value = res
    options.value.unshift({ id: 0, name: '主菜单' })
  })
  if (props.layer.row) {
    form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
  } else {

  }
}
const submit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid, fields) => {
    if (valid) {
      let params = form.value
      if (props.layer.row) {
        updateForm(params)
      } else {
        addForm(params)
      }
    } else {
      console.log('error submit!', fields)
    }
  })
}
const addForm = (params: CategroyParms) => {
  addCategroy(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '新增成功'
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
}
const updateForm = (params: updateCategroyParms) => {
  updateCategroy(params)
    .then(res => {
      ElMessage({
        type: 'success',
        message: '编辑成功'
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
}
</script>

<style lang="scss" scoped></style>