/**@name  表单信息响应体 */
export interface FormConfigInfoRes {
  /**
   *@name 表单内容
   */
  content: string
  /**
   *@name  创建时间
   */
  createTime?: Date
  /**
   *@name  表单模板id
   */
  id?: number
  /**
   *@name  表单简介
   */
  info?: string
  /**
   *@name  表单名称
   */
  name?: string
  /**
   *@name  更新时间
   */
  updateTime?: Date

}
/**@name 表单内容响应体 */
export interface ConfigInfoRes {
  /**
   *@name 表单内字段的值
   */
  [key: string]: string|number
}
/**@name 表单提交保存请求体 */
export interface SaveFormConfigParams {
  /**
   *@name  字段值列表
   */
  fields: Fields[]
  /**
   *@name  表单名称
   */
  id: number|string
  /**
   *@name  排序
   */
  sort: number
  /**
   *@name  状态（1：开启；0：关闭；）
   */
  status?: boolean
}
export interface Fields {
  /**
   *@name  字段名称
   */
  name: string
  /**
   *@name  字段显示文字
   */
  title: string
  /**
   *@name  字段值
   */
  value: string
}
/**@name 表单创建请求体 */
export interface FormConfigSaveParams {
  /**
   *@name  表单内容
   */
  content: string
  /**
   *@name  表单简介
   */
  info: string
  /**
   *@name  表单名称
   */
  name: string
}
/**@name 表单修改请求体 */
export interface FormConfigEditParams extends FormConfigSaveParams {
     /**
   *@name  id
   */
  id?:number
}

/**@name 表单列表请求体 */
export interface FormConfigListParams {
         /**
   *@name  关键字
   */
  keywords:string
    /*** @name 当前页*/
  page?: number
  /*** @name 每页条数*/
  limit?: number

}
/**@name 表单列表响应体 */
export interface FormConfigListRes {
    /**
     * 表单内容
     */
    content?: string;
    /**
     * 创建时间
     */
    createTime?: Date;
    /**
     * 表单模板id
     */
    id?: number;
    /**
     * 表单简介
     */
    info?: string;
    /**
     * 表单名称
     */
    name?: string;
    /**
     * 更新时间
     */
    updateTime?: Date;
    loading?:boolean
}
