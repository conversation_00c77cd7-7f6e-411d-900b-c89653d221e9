<template>
  <div class="layout-container">
    <div class="layout-container-form flex space-between">
      <div class="layout-container-form-handle">
        <el-button type="primary" :icon="Plus" @click="handleAdd">
          {{ $t('message.common.add') }}
        </el-button>
      </div>
      <div class="layout-container-form-search">
        <el-input
          v-model="query.realName"
          placeholder="请输入账号或姓名"
        ></el-input>
        <el-button
          type="primary"
          :icon="Search"
          class="search-btn"
          @click="getTableData(true)"
        >
          {{ $t('message.common.search') }}
        </el-button>
      </div>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column prop="id" label="ID" align="center" width="80" />
        <el-table-column prop="realName" label="姓名" align="center" />
        <el-table-column prop="account" label="账号" align="center" />
        <el-table-column prop="phone" label="手机号" align="center" />
        <el-table-column prop="roleNames" label="身份" align="center" />
        <el-table-column prop="lastTime" label="最后登录时间" align="center" />
        <el-table-column prop="lastIp" label="最后登录IP" align="center" />
        <el-table-column prop="status" label="状态" align="center">
          <template #default="scope">
            <span class="statusName">
              {{ scope.row.status ? '启用' : '禁用' }}
            </span>
            <el-switch
              v-model="scope.row.status"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="true"
              :inactive-value="false"
              :loading="scope.row.loading"
              @change="handleUpdateStatus(scope.row)"
            ></el-switch>
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            <el-button @click="handleEdit(scope.row)">
              {{ $t('message.common.update') }}
            </el-button>
            <el-popconfirm
              :title="$t('message.common.delTip')"
              @confirm="handleDel(scope.row)"
            >
              <template #reference>
                <el-button type="danger">
                  {{ $t('message.common.del') }}
                </el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </Table>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import {
    getAdminList,
    updateAdminStatus,
    delAdmin,
  } from '@/api/systemSetting'
  import { LayerInterface } from '@/components/layer/index.vue'
  import { ElMessage } from 'element-plus'
  import Table from '@/components/table/index.vue'
  import Layer from './layer.vue'
  import { Plus, Delete, Search } from '@element-plus/icons-vue'
  import type { AdminListRes } from '@/type/systemSetting.type'
  // 存储搜索用的数据
  const query = reactive<{ realName?: string }>({
    realName: '',
  })
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<AdminListRes[]>([])
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getAdminList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  // 删除功能
  const handleDel = (data: AdminListRes) => {
    let params = data.id
    delAdmin({ id: params }).then(res => {
      ElMessage({
        type: 'success',
        message: '删除成功',
      })
      getTableData(tableData.value.length === 1 ? true : false)
    })
  }
  // 新增弹窗功能
  const handleAdd = () => {
    layer.title = '新增数据'
    layer.show = true
    delete layer.row
  }
  // 编辑弹窗功能
  const handleEdit = (row: any) => {
    layer.title = '编辑数据'
    layer.row = row
    layer.show = true
  }
  // 状态编辑功能
  const handleUpdateStatus = (row: any) => {
    if (!row.id) {
      return
    }
    row.loading = true
    let params = {
      id: row.id,
      status: row.status + '',
    }
    updateAdminStatus(params)
      .then(res => {
        ElMessage({
          type: 'success',
          message: '状态变更成功',
        })
      })
      .catch(err => {
        ElMessage({
          type: 'error',
          message: '状态变更失败',
        })
      })
      .finally(() => {
        row.loading = false
      })
  }
  getTableData(true)
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }
</style>
