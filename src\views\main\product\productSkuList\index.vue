<template>
  <div class="layout-container" style="padding: 15px">
    <el-tabs v-model="query.type" class="demo-tabs" @tab-click="clickTab">
      <el-tab-pane
        :label="item.name + `(${item.count})`"
        :name="item.type"
        v-for="item in tabsList"
        :key="item.type"
      ></el-tab-pane>
    </el-tabs>
    <div class="flex justify-between">
      <el-form label-width="80px" :inline="false">
        <el-form-item label-width="0">
          <el-form-item label="商品分类">
            <el-cascader
              :options="options"
              :props="props"
              clearable
              v-model="query.cateId"
              @change="getTableData(true)"
            />
          </el-form-item>
          <el-form-item label="商品搜索">
            <div class="layout-container-form-search">
              <el-input
                v-model="query.keywords"
                placeholder="请输入关键字"
                clearable
                @clear="getTableData(true)"
              ></el-input>
              <el-button
                type="primary"
                :icon="Search"
                class="search-btn"
                @click="getTableData(true)"
              >
                {{ $t('message.common.search') }}
              </el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <el-button
              class="search-btn"
              style="margin: 0 10px"
              @click="handleOutbound"
            >
              出库
            </el-button>
          </el-form-item>
          <el-form-item>
            <el-button class="search-btn" @click="changeStock(1)">
              入库
            </el-button>
          </el-form-item>
        </el-form-item>
      </el-form>
      <div class="mr-2rem">
        <button @click="openSmartDialog" class="trigger-button">
          <svg
            class="icon"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
          >
            <path
              d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"
            />
            <polyline points="14,2 14,8 20,8" />
            <line x1="16" y1="13" x2="8" y2="13" />
            <line x1="16" y1="17" x2="8" y2="17" />
            <polyline points="10,9 9,9 8,9" />
          </svg>
          智能生成货单
        </button>
      </div>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
        @sort-change="handleSortChange"
      >
        <el-table-column prop="id" label="id" align="center" width="80" />
        <el-table-column label="商品图" align="center">
          <template #default="scope">
            <imagePreview :imgurl="scope.row.image"></imagePreview>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" align="center" />
				<el-table-column prop="attrValueNo" label="产品编码" align="center" />
        <el-table-column prop="suk" label="商品规格" align="center" />
        <el-table-column
          prop="unitGroupStr"
          label="库存"
          align="center"
        />
        <el-table-column prop="minUnitStock" label="数量" align="center" sortable="custom" sort-by="stock" />
      </Table>
    </div>

    <!-- 智能生成货单模态框 -->
    <div
      v-if="smartDialogVisible"
      class="modal-overlay"
      @keydown="handleKeydown"
      tabindex="-1"
    >
      <div class="modal-content" @click.stop>
        <!-- 头部区域 -->
        <div class="modal-header">
          <div class="header-content">
            <div class="header-icon">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path
                  d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8z"
                />
                <path d="M3.27 6.96L12 12.01l8.73-5.05" />
                <path d="M12 22.08V12" />
              </svg>
            </div>
            <div>
              <h2 class="modal-title !ml-[-60%]">商品信息录入</h2>
              <p class="modal-description">
                请按照指定格式输入商品信息，支持批量录入
              </p>
            </div>
          </div>
        </div>

        <!-- 内容区域 -->
        <div class="modal-body">
          <!-- 格式说明卡片 -->
          <div class="instruction-card">
            <div class="instruction-content">
              <div class="instruction-icon">
                <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path
                    d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"
                  />
                </svg>
              </div>
              <div class="instruction-text">
                <div class="instruction-title">输入格式说明</div>
                <div class="instruction-desc">
                  每行输入一个商品，格式为：
                  <code class="format-code">商品名称,数量,商品单位</code>
                </div>
                <div class="example-box">
                  <div class="example-title">示例格式：</div>
                  <div class="example-list">
                    <div class="example-item">
                      <svg
                        class="check-icon"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                        <polyline points="22,4 12,14.01 9,11.01" />
                      </svg>
                      火山石烤肠,10,包
                    </div>
                    <div class="example-item">
                      <svg
                        class="check-icon"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                        <polyline points="22,4 12,14.01 9,11.01" />
                      </svg>
                      矿泉水,20,瓶
                    </div>
                    <div class="example-item">
                      <svg
                        class="check-icon"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                      >
                        <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
                        <polyline points="22,4 12,14.01 9,11.01" />
                      </svg>
                      笔记本,5,本
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="input-section">
            <div class="input-header">
              <label for="product-info" class="input-label">商品信息</label>
              <span
                :class="[
                  'product-badge',
                  productCount > 0 ? 'badge-active' : 'badge-inactive',
                ]"
              >
                {{ productCount }} 个商品
              </span>
            </div>
            <textarea
              id="product-info"
              v-model="productInputText"
              @input="handleInputChange"
              @keydown.stop="handleKeydown"
              placeholder="火山石烤肠,10,包&#10;矿泉水,20,瓶&#10;笔记本,5,本"
              :class="[
                'product-textarea',
                {
                  error: inputError,
                  success: productInputText.trim() && !inputError,
                },
              ]"
              :disabled="isSubmitting"
            ></textarea>

            <!-- 错误提示 -->
            <div v-if="inputError" class="error-message">
              <svg
                class="error-icon"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <circle cx="12" cy="12" r="10" />
                <path d="M15 9l-6 6" />
                <path d="M9 9l6 6" />
              </svg>
              {{ inputError }}
            </div>

            <!-- 成功提示 -->
            <div v-else-if="productInputText.trim()" class="success-message">
              <svg
                class="success-icon"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <path d="M20 6L9 17l-5-5" />
              </svg>
              格式正确，准备提交
            </div>

            <!-- 快捷键提示 -->
            <div
              v-if="productInputText.trim() && !inputError"
              class="shortcut-hint"
            >
              按 Ctrl+Enter 快速提交，按 Esc 关闭
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="modal-footer">
          <button
            @click="closeSmartDialog"
            :disabled="isSubmitting"
            class="cancel-button"
          >
            取消
          </button>
          <button
            @click.stop="handleSmartSubmit"
            :disabled="!productInputText.trim() || !!inputError || isSubmitting"
            class="submit-button"
          >
            <svg
              v-if="isSubmitting"
              class="loading-icon"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
            >
              <path d="M21 12a9 9 0 11-6.219-8.56" />
            </svg>
            {{ isSubmitting ? '生成中...' : '确认提交' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { defineComponent, ref, reactive, computed } from 'vue'
  import { Page } from '@/components/table/type'
  import { LayerInterface } from '@/components/layer/index.vue'
  import skuInfo from './skuInfo.vue'
  import { ElMessage } from 'element-plus'
  import { getAttrValueList } from '@/api/product'
  import { treeCategroy } from '@/api/categoryApi'
  import Table from '@/components/table/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  import {
    Plus,
    Delete,
    Search,
    Check,
    Edit,
    InfoFilled,
    CircleCheckFilled,
    Box,
  } from '@element-plus/icons-vue'
  import type { TabsPaneContext } from 'element-plus'
  import type { ProductHeaderRes } from '@/type/product.type'
  import { useRouter } from 'vue-router'
  import { TreeCategroyRes } from '@/type/category.type'
  import type { ProductListParams, ProductListRes } from '@/type/product.type'
  import { intelligencePurchaseOrder } from '@/api/order'
  import {
    intelligencePurchaseOrderList,
    setIntelligenceOrder,
  } from './useController'
  const router = useRouter()
  // 存储搜索用的数据
  const query = reactive({
    keywords: '',
    cateId: '',
    type: '',
  })

  // 库存排序状态
  const currentSort = ref<{ prop: string; order: string } | null>(null)
  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增商品',
    showButton: true,
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref<ProductListRes[]>([])
  const activeIndex = ref(0)

  // 智能生成货单对话框相关
  const smartDialogVisible = ref(false)
  const productInputText = ref('')

  // 处理表格排序变化
  const handleSortChange = ({ prop, order }: { prop: string; order: string | null }) => {
    currentSort.value = order ? { prop, order } : null
    getTableData(true)
  }

  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页
  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params: any = {
      page: page.index,
      limit: page.size,
      ...query,
    }

    // 添加库存排序参数
    if (currentSort.value && currentSort.value.prop === 'minUnitStock') {
      params.sortColumn = 'stock'
      params.sortType = currentSort.value.order === 'ascending' ? 'asc' : 'desc'
    }
    getAttrValueList(params)
      .then(res => {
        let data = res.list
        data.forEach(d => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  const tabsList = ref<ProductHeaderRes[]>([])

  //切换tab
  const clickTab = (tab: TabsPaneContext, event: Event) => {
    getTableData(true)
  }
  //商品分类选择框
  const options = ref<TreeCategroyRes[]>([])
  const props = {
    value: 'id',
    label: 'name',
    children: 'child',
    emitPath: false,
  }
  treeCategroy({ type: 1, status: -1 }).then(res => [(options.value = res)])

  const changeStock = (type: number) => {
    router.push({
      path: '/product/skuSelectList',
      query: { type },
    })
  }

  const handleOutbound = () => {
    router.push({
      path: '/product/orderOutbound',
    })
  }

  // 智能生成货单相关方法
  const isSubmitting = ref(false)
  const inputError = ref('')

  const openSmartDialog = () => {
    smartDialogVisible.value = true
    // 延迟聚焦到文本框
    setTimeout(() => {
      const textarea = document.getElementById('product-info')
      if (textarea) {
        textarea.focus()
      }
    }, 300)
  }

  const closeSmartDialog = () => {
    // 提交中不允许关闭
    if (isSubmitting.value) {
      return
    }

    smartDialogVisible.value = false
    // 延迟清空内容，避免闪烁
    setTimeout(() => {
      productInputText.value = ''
      inputError.value = ''
    }, 250)
  }

  // 验证输入格式
  const validateInput = (text: string) => {
    if (!text.trim()) {
      return '请输入商品信息'
    }

    const lines = text.split('\n').filter(line => line.trim())
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim()
      const parts = line.split(',')
      if (parts.length !== 3) {
        return `第${i + 1}行格式错误：应为"商品名称,数量,单位"`
      }
      if (!parts[0].trim() || !parts[1].trim() || !parts[2].trim()) {
        return `第${i + 1}行信息不完整`
      }
      if (isNaN(Number(parts[1].trim()))) {
        return `第${i + 1}行数量必须为数字`
      }
    }
    return ''
  }

  // 实时验证
  const handleInputChange = () => {
    inputError.value = validateInput(productInputText.value)
  }

  const handleSmartSubmit = async () => {
    // 防止重复提交
    if (isSubmitting.value) {
      return
    }

    const error = validateInput(productInputText.value)
    if (error) {
      inputError.value = error
      ElMessage.warning(error)
      return
    }

    isSubmitting.value = true
    inputError.value = ''

    try {
      const res = await intelligencePurchaseOrder({
        prompt: productInputText.value,
      })
      // 使用新的内存级别状态管理
      setIntelligenceOrder(res.attrValueResponseList)
      ElMessage.success('货单生成成功！')
      closeSmartDialog()
      router.push('/product/orderOutbound')
    } catch (error) {
      console.log(error)
    } finally {
      isSubmitting.value = false
    }
  }

  // 键盘快捷键支持
  const handleKeydown = (event: KeyboardEvent) => {
    if (event.key === 'Escape') {
      closeSmartDialog()
    } else if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault()
      event.stopPropagation() // 阻止事件冒泡
      handleSmartSubmit()
    }
  }

  // 计算商品数量
  const productCount = computed(() => {
    return productInputText.value.split('\n').filter(line => line.trim()).length
  })

  const getTabsHeader = () => {
    /*getTabsHearder().then(res => {
            tabsList.value = res
            getTableData(true)
        })*/
    getTableData(true)
  }
  //获取tab栏
  getTabsHeader()
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }

  .picture {
    width: 30px;
    height: 30px;
  }

  .flex-center {
    margin-left: 80px;
  }

  .newline-text {
    white-space: pre-line;
    line-height: 1.4;
  }

  .trigger-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }

    &:hover {
      background: linear-gradient(135deg, #337ecc 0%, #2b6cb0 100%);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
      transform: translateY(-1px);

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
    }

    .icon {
      width: 16px;
      height: 16px;
      stroke-width: 2.5;
      flex-shrink: 0;
    }
  }

  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    animation: fadeIn 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 20px;
  }

  .modal-content {
    background: white;
    border-radius: 16px;
    max-width: 580px;
    width: 100%;
    max-height: calc(100vh - 40px);
    overflow: hidden;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 0 1px rgba(0, 0, 0, 0.05);
    animation: slideUp 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .modal-header {
    background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
    color: white;
    padding: 20px 24px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%
      );
      pointer-events: none;
    }
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 12px;
    position: relative;
    z-index: 1;
  }

  .header-icon {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 10px;
    padding: 10px;
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);

    svg {
      width: 20px;
      height: 20px;
      stroke-width: 2.5;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }
  }

  .modal-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 4px 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    letter-spacing: -0.025em;
  }

  .modal-description {
    color: rgba(255, 255, 255, 0.85);
    margin: 0;
    font-size: 13px;
    font-weight: 400;
    line-height: 1.4;
  }

  .modal-body {
    padding: 20px 24px 24px;
    display: flex;
    flex-direction: column;
    gap: 20px;
    background: #fafbfc;
  }

  .instruction-card {
    background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 12px;
    padding: 16px;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #ffc107, #ff9800);
    }
  }

  .instruction-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
  }

  .instruction-icon {
    background: linear-gradient(135deg, #ffc107 0%, #ff9800 100%);
    border-radius: 8px;
    padding: 8px;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);

    svg {
      width: 18px;
      height: 18px;
      stroke-width: 2.5;
      color: white;
      filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
    }
  }

  .instruction-text {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .instruction-title {
    font-weight: 600;
    color: #e65100;
    font-size: 15px;
    margin-bottom: 2px;
    letter-spacing: -0.01em;
  }

  .instruction-desc {
    color: #f57c00;
    font-size: 13px;
    line-height: 1.5;
    font-weight: 400;
  }

  .format-code {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    color: white;
    padding: 3px 8px;
    border-radius: 4px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 1px 2px rgba(245, 124, 0, 0.2);
  }

  .example-box {
    background: white;
    border: 1px solid rgba(255, 193, 7, 0.4);
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .example-title {
    font-size: 11px;
    color: #f57c00;
    font-weight: 600;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  .example-list {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }

  .example-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 13px;
    color: #4a5568;
    padding: 2px 0;
    transition: color 0.2s ease;

    &:hover {
      color: #2d3748;
    }
  }

  .check-icon {
    width: 14px;
    height: 14px;
    stroke-width: 2.5;
    color: #38a169;
    flex-shrink: 0;
  }

  .input-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
    background: white;
    border-radius: 10px;
    padding: 16px;
    border: 1px solid #e2e8f0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  }

  .input-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2px;
  }

  .input-label {
    font-size: 14px;
    font-weight: 600;
    color: #2d3748;
    letter-spacing: -0.01em;
  }

  .product-badge {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    transition: all 0.2s ease;
  }

  .badge-active {
    background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
    color: white;
    box-shadow: 0 1px 3px rgba(64, 158, 255, 0.3);
  }

  .badge-inactive {
    background: #f7fafc;
    color: #718096;
    border: 1px solid #e2e8f0;
  }

  .product-textarea {
    min-height: 120px;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    padding: 12px;
    resize: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    background: #fafbfc;

    &:focus {
      border-color: #409eff;
      background: white;
      box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.1);
    }

    &::placeholder {
      color: #a0aec0;
      font-style: italic;
    }

    &.success {
      border-color: #38a169;
      background: #f0fff4;
    }

    &.error {
      border-color: #e53e3e;
      background: #fef5f5;
      animation: shake 0.3s ease-in-out;
    }

    &:disabled {
      background: #f7fafc;
      color: #a0aec0;
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  .success-message {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #38a169;
    font-size: 12px;
    font-weight: 500;
    padding: 6px 0;
    animation: slideInUp 0.3s ease;
  }

  .success-icon {
    width: 14px;
    height: 14px;
    stroke-width: 2.5;
    flex-shrink: 0;
  }

  .error-message {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #e53e3e;
    font-size: 12px;
    font-weight: 500;
    padding: 6px 0;
    animation: slideInUp 0.3s ease;
  }

  .error-icon {
    width: 14px;
    height: 14px;
    stroke-width: 2.5;
    flex-shrink: 0;
  }

  .shortcut-hint {
    font-size: 11px;
    color: #718096;
    text-align: center;
    padding: 4px 0;
    font-style: italic;
    opacity: 0.8;
    animation: fadeInSlow 0.5s ease;
  }

  .modal-footer {
    background: linear-gradient(to bottom, #fafbfc 0%, #f4f6f8 100%);
    border-top: 1px solid #e2e8f0;
    padding: 16px 24px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  .cancel-button {
    background: white;
    color: #4a5568;
    border: 1.5px solid #e2e8f0;
    border-radius: 6px;
    padding: 8px 20px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(0, 0, 0, 0.03),
        transparent
      );
      transition: left 0.5s;
    }

    &:hover {
      background: #f7fafc;
      border-color: #cbd5e0;
      color: #2d3748;

      &::before {
        left: 100%;
      }
    }

    &:active {
      transform: scale(0.98);
    }
  }

  .submit-button {
    background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 20px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.25);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 6px;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      transition: left 0.5s;
    }

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #337ecc 0%, #2b6cb0 100%);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.35);
      transform: translateY(-1px);

      &::before {
        left: 100%;
      }
    }

    &:active:not(:disabled) {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(64, 158, 255, 0.25);
    }

    &:disabled {
      background: linear-gradient(135deg, #a0aec0 0%, #9ca3af 100%);
      cursor: not-allowed;
      box-shadow: none;
      opacity: 0.6;
    }
  }

  .loading-icon {
    width: 14px;
    height: 14px;
    stroke-width: 2.5;
    animation: spin 1s linear infinite;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      backdrop-filter: blur(0px);
    }
    to {
      opacity: 1;
      backdrop-filter: blur(4px);
    }
  }

  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(32px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes slideInUp {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shake {
    0%,
    100% {
      transform: translateX(0);
    }
    25% {
      transform: translateX(-4px);
    }
    75% {
      transform: translateX(4px);
    }
  }

  @keyframes spin {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes fadeInSlow {
    from {
      opacity: 0;
    }
    to {
      opacity: 0.8;
    }
  }

  @media (max-width: 640px) {
    .modal-content {
      width: 95%;
      margin: 20px;
    }

    .modal-body {
      padding: 16px;
    }

    .modal-header {
      padding: 16px;
    }

    .instruction-content {
      flex-direction: column;
      gap: 12px;
    }
  }
</style>
