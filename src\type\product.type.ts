/**@name  商品列表头部信息 */
export interface ProductHeaderRes {
  count?: number
  name?: string
  type?: number
}
/**@name  商品列表响应体 */

export interface ProductListRes {
  /**
   * 活动显示排序 0=默认，1=秒杀，2=砍价，3=拼团
   */
  activity?: string[]
  /**
   * 商品属性
   */
  attr?: StoreProductAttr[]
  /**
   * 商品属性详情
   */
  attrValue?: AttrnameResponse[]
  /**
   * 帮砍次数|砍价专用
   */
  bargainNum?: number
  /**
   * 分类id
   */
  cateId?: string
  /**
   * 分类中文
   */
  cateStr?: string
  /**
   * 商品描述
   */
  content?: string
  /**
   * 优惠券Ids
   */
  couponIds?: number[]
  /**
   * 拼团订单有效时间(小时)|拼团专用
   */
  effectiveTime?: number
  /**
   * 虚拟销量
   */
  ficti?: number
  /**
   * 展示图
   */
  flatPattern?: string
  /**
   * 获得积分
   */
  giveIntegral?: number
  /**
   * 商品id
   */
  id?: number
  /**
   * 商品图片
   */
  image?: string
  /**
   * 简介|拼团专用
   */
  info?: string
  /**
   * 是否优惠
   */
  isBenefit?: boolean
  /**
   * 是否精品
   */
  isBest?: boolean
  /**
   * 是否品牌好货
   */
  isGood?: boolean
  /**
   * 是否热卖
   */
  isHot?: boolean
  /**
   * 是否新品
   */
  isNew?: boolean
  /**
   * 商品状态|拼团专用
   */
  isShow?: boolean
  /**
   * 是否单独分佣
   */
  isSub?: boolean
  /**
   * 关键字
   */
  keyword?: string
  /**
   * 当天参与秒杀次数|秒杀专用
   */
  num?: number
  /**
   * 每个订单可购买数量|拼团专用
   */
  onceNum?: number
  /**
   * 参团人数|拼团专用
   */
  people?: number
  /**
   * 帮助砍价好友人数|砍价专用
   */
  peopleNum?: number
  /**
   * 产品ID
   */
  productId?: number
  /**
   * 轮播图
   */
  sliderImage?: string
  /**
   * 排序
   */
  sort?: number
  /**
   * 规格 0单 1多
   */
  specType?: boolean
  /**
   * 砍价开启时间|砍价专用
   */
  startTime?: number
  /**
   * 秒杀开启时间|秒杀专用
   */
  startTimeStr?: string
  /**
   * 状态 0=关闭 1=开启|营销商品用
   */
  status?: number
  /**
   * 砍价结束时间|砍价专用
   */
  stopTime?: number
  /**
   * 秒杀结束时间|秒杀专用
   */
  stopTimeStr?: string
  /**
   * 商品简介
   */
  storeInfo?: string
  /**
   * 商品名称
   */
  productName?: string
  /**
   * 运费模板ID
   */
  tempId?: number
  /**
   * 运费模板ID|秒杀商品专用
   */
  timeId?: number
  /**
   * 砍价活动名称|砍价专用
   */
  title?: string
  /**
   * 单位id
   */
  unitId?: string
  /**
   * 虚拟成团百分比|拼团专用
   */
  virtualRation?: number
  loading?: boolean
}

/**
 * 产品属性，商品属性表
 */
export interface StoreProductAttr {
  /**
   * 属性名
   */
  attrName?: string
  /**
   * 属性值
   */
  attrValues?: string
  /**
   * attrId
   */
  id?: number
  /**
   * 是否删除,0-否，1-是
   */
  isDel?: boolean
  /**
   * 商品ID
   */
  productId?: number
  /**
   * 活动类型 0=商品，1=秒杀，2=砍价，3=拼团
   */
  type?: number
}

/**
 * 商品属性详情，商品属性值响应对象
 */
export interface AttrnameResponse {
  /**
   * attrValue字段
   */
  attrValue?: string
  /**
   * 一级返佣
   */
  brokerage?: number
  /**
   * 二级返佣
   */
  brokerageTwo?: number
  /**
   * 成本价
   */
  cost?: number
  /**
   * ID
   */
  id?: number
  /**
   * 图片
   */
  image?: string
  /**
   * 砍价商品最低价|砍价专用字段
   */
  minPrice?: number
  /**
   * 原价
   */
  otPrice?: number
  /**
   * 属性金额
   */
  price?: number
  /**
   * 商品ID
   */
  productId?: number
  /**
   * 活动限购数量
   */
  quota?: number
  /**
   * 活动限购数量显示
   */
  quotaShow?: number
  /**
   * 销量
   */
  sales?: number
  /**
   * 属性对应的库存
   */
  stock?: number
  /**
   * sku
   */
  suk?: string
  /**
   * 活动类型 0=商品，1=秒杀，2=砍价，3=拼团
   */
  type?: number
  /**
   * 体积
   */
  volume?: number
  /**
   * 重量
   */
  weight?: number
  /**
   * 单位库存
   */
  unitNums?: number[]
}
/**@name  商品列表请求体 */

export interface ProductListParams {
  type: number
  cateId?: string
  keywords?: string
  isHot?: boolean
  page?: number
  limit?: number
  /**
   * 排序字段
   */
  sortColumn?: string
  /**
   * 排序类型 asc-升序 desc-降序
   */
  sortType?: 'asc' | 'desc'
  /**
   * 额外筛选参数 (如: stockLack-库存不足)
   */
  extra?: string
}
/**@name  商品规格列表请求体 */

export interface RuleListParams {
  keywords?: string
  page?: number
  limit?: number
}
/**@name  商品规格列表响应体 */

export interface RuleListRes {
  id: string
  ruleName: string
  ruleValue: string
}
/**@name  商品规格请求体*/

export interface RuleParams {
  id?:number
  ruleName: string
  ruleValue: any
}

/**@name  出入库请求体*/

export interface ProductStockChangeRequest {
  productId:number
  skuId: number
  type: number
  nums: number[]
}
