<template>
  <div class="layout-container">
    <div style="padding: 15px">
      <el-form label-position="right" label-width="100px">
        <el-form-item label="时间选择">
          <timepicker v-model="query.dateLimit" @change="getTableData(true)" />
        </el-form-item>
        <Unfold>
          <el-form-item label="回复状态">
            <el-radio-group
              v-model="query.isReply"
              @change="getTableData(true)"
            >
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="true">已回复</el-radio-button>
              <el-radio-button label="false">未回复</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-row>
            <el-form-item label="商品名称">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.productSearch"
                placeholder="请输入商品名称"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>
            <el-form-item label="手机号">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.phone"
                placeholder="请输入手机号"
              ></el-input>
            </el-form-item>
            <el-form-item label="昵称">
              <el-input
                clearable
                @clear="getTableData(true)"
                v-model.trim="query.nickname"
                placeholder="请输入昵称"
                style="margin-right: 10px"
              ></el-input>
            </el-form-item>

            <el-button
              type="primary"
              :icon="Search"
              class="search-btn"
              @click="getTableData(true)"
            >
              {{ $t('message.common.search') }}
            </el-button>
          </el-row>
        </Unfold>
      </el-form>
    </div>
    <div class="layout-container-table">
      <Table
        ref="table"
        v-model:page="page"
        v-loading="loading"
        :data="tableData"
        @getTableData="getTableData"
      >
        <el-table-column
          prop="oid"
          label="订单ID"
          align="center"
          :show-overflow-tooltip="false"
        />
        <el-table-column prop="productName" label="商品名称" align="center" />
        <el-table-column prop="nickname" label="昵称" align="center" />
        <el-table-column prop="phone" label="手机号" align="center" />
        <el-table-column prop="comment" label="评论内容" align="center" />
        <el-table-column label="评论图片" align="center">
          <template #default="scope">
            <img src="" alt="" srcset="" />
            <imagePreview :imgurl="scope.row.picArray" :type="3"></imagePreview>
          </template>
        </el-table-column>
        <el-table-column prop="isReply" label="回复状态" align="center">
          <template #default="scope">
            <div>
              {{ scope.row.isReply ? '已回复' : '未回复' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="merchantReplyContent"
          label="管理员回复内容"
          align="center"
        />
        <el-table-column prop="createTime" label="创建时间" align="center" />
        <el-table-column prop="updateTime" label="更新时间" align="center" />
        <el-table-column
          :label="$t('message.common.handle')"
          align="center"
          fixed="right"
          width="200"
        >
          <template #default="scope">
            .
            <el-button @click="handleEdit(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </Table>
      <Layer :layer="layer" @getTableData="getTableData" v-if="layer.show" />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive } from 'vue'
  import { Page } from '@/components/table/type'
  import { getProductReply } from '@/api/product'
  import { LayerInterface } from '@/components/layer/index.vue'
  import Table from '@/components/table/index.vue'
  import { Search } from '@element-plus/icons-vue'
  import Layer from './layer.vue'
  import Unfold from '@/components/unfold/index.vue'
  import timepicker from '@/components/timePicker/index.vue'
  import imagePreview from '@/components/imagePreview/index.vue'
  // 存储搜索用的数据
  const query = reactive({
    productSearch: '',
    isReply: '',
    nickname: '',
    phone: '',
    dateLimit: '',
  })
  // 分页参数, 供table使用
  const page: Page = reactive({
    index: 1,
    size: 20,
    total: 0,
  })
  const loading = ref(true)
  const tableData = ref([])
  // 获取表格数据
  // params <init> Boolean ，默认为false，用于判断是否需要初始化分页

  const getTableData = (init: Boolean) => {
    loading.value = true
    if (init) {
      page.index = 1
    }
    let params = {
      page: page.index,
      limit: page.size,
      ...query,
    }
    getProductReply(params)
      .then(res => {
        let data = res.list
        data.forEach((d: any) => {
          d.loading = false
        })
        tableData.value = data
        page.total = Number(res.total)
      })
      .catch(error => {
        tableData.value = []
        page.index = 1
        page.total = 0
      })
      .finally(() => {
        loading.value = false
      })
  }
  getTableData(true)

  // 弹窗控制器
  const layer: LayerInterface = reactive({
    show: false,
    title: '新增',
    showButton: false,
  })

  // 查看
  const handleEdit = (row: any) => {
    layer.title = '查看详情'
    layer.row = row
    layer.show = true
  }
</script>

<style lang="scss" scoped>
  .statusName {
    margin-right: 10px;
  }
</style>
