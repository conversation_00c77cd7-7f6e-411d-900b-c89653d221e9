
// 提现记录列表
export interface ExtractListParams {
     /**
     * @name today,yesterday,lately7,lately30,month,year,/yyyy-MM-dd hh:mm:ss,yyyy-MM-dd hh:mm:ss/
     */
    dateLimit?: string;
     /**
     * @name 1余额提现
     */
    extractTarget?: number;
     /**
     * @name alipay = 支付宝 weixin = 微信
     */
    extractType?: string;
    /**
     * @name 每页条数
     */
    limit?: number;
    /**
     * @name 当前页数
     */
    page?: number;
    /**
     * @name 手机号
     */
    phone?: string;
     /**
     * @name -1 未通过 0 审核中 1 已提现
     */
    status?: number;
    
}




/**
 * UserExtract对象，用户提现表
 */
export interface ExtractListRes {
    /**
     * @name today,yesterday,lately7,lately30,month,year,/yyyy-MM-dd hh:mm:ss,yyyy-MM-dd hh:mm:ss/
     */
    dateLimit?: string;
    /**
     * @name 1余额提现
     */
    extractTarget?: number;
    /**
     * @name alipay = 支付宝 weixin = 微信
     */
    extractType?: string;
    /**
     * @name 手机号
     */
    phone?: string;
    /**
     * @name -1 未通过 0 审核中 1 已提现
     */
    status?: number;
    loading?:boolean;
}


/**
 * 提现审核驳回请求接口
 */
export interface ExtractRejectParams {
    /**
     * @name 驳回原因
     */
    failMsg: string;
    /**
     * @name 提现id
     */
    id?: number;
}

/**
 * 充值记录
 */
export interface RecordsListParams {
     /**
     * @name today,yesterday,lately7,lately30,month,year,/yyyy-MM-dd hh:mm:ss,yyyy-MM-dd hh:mm:ss/
     */
    dateLimit?: string;
    /**
     * @name 每页条数
     */
    limit?: number;
     /**
     * @name 订单号
     */
    orderNum?: string;
     /**
     * @name 当前页数
     */
    page?: number;
    /**
     * @name 支付类型
     */
    payType?: string;
    /**
     * @name 手机号
     */
    phone?: string;
     /**
     * @name 充值目标 1余额 2通证
     */
    rechargeTarget?: number;
   /**
     * @name -1 取消 0 待支付 1 已支付
     */
    status?: number;
}



/**
 * 用户充值记录
 */
export interface RecordsListRes {
    /**
     * @name 充值时间
     */
    createTime?: Date;
    id?: number;
    /**
     * @name 订单号
     */
    orderNum?: string;
    /**
     * @name 是否充值
     */
    paid?: boolean;
    /**
     * @name 支付渠道
     */
    payChannel?: string;
    /**
     * @name 充值支付时间
     */
    payTime?: Date;
    /**
     * @name 支付类型
     */
    payType?: string;
    /**
     * @name 手机号
     */
    phone?: string;
    /**
     * @name 充值金额
     */
    price?: number;
    /**
     * @name 充值目标 1余额 2通证
     */
    rechargeTarget?: number;
    /**
     * @name -1 取消 0 待支付 1 已支付
     */
    status?: number;
    /**
     * @name 充值用户UID
     */
    uid?: number;
    loading?:boolean;
}

/**
 * 余额记录
 */
export interface MoneyRecordParams {
    /**
     * @name 途径
     */
    channel?: number;
    dateLimit?: string;
     /**
     * @name 每页条数
     */
    limit?: number;
     /**
     * @name 当前页数
     */
    page?: number;
     /**
     * @name 手机号
     */
    phone?: string;
    target?: number;
    type?: number;
}


/**
 * 余额记录
 */
export interface MoneyRecordRes {
    /**
     * @name 剩余
     */
    balance?: number;
    /**
     * @name 途径
     */
    channel?: number;
    /**
     * @name 创建时间
     */
    createTime?: Date;
    /**
     * @name 记录id
     */
    id?: number;
    /**
     * @name 数额
     */
    money?: number;
    /**
     * @name 手机号
     */
    phone?: string;
    /**
     * @name 备注
     */
    remark?: string;
    /**
     * @name 标题
     */
    title?: string;
    /**
     * @name 类型 1增加  2扣减
     */
    type?: number;
    /**
     * @name 用户id
     */
    uid?: number;
}

/**
 *  积分明细，余额明细类型
 */
export interface ChannelRes {
    [key: string]: string 
}


/**
 * 积分记录列表
 */
export interface IntegralRecordListParams {
     /**
     * @name 途径
     */
    channel?: number;
    dateLimit?: string;
     /**
     * @name 每页条数
     */
    limit?: number;
     /**
     * @name 当前页面
     */
    page?: number;
     /**
     * @name 手机号
     */
    phone?: string;
    
    target?: number;
    /**
     * @name 类型 1增加  2扣减
     */
    type?: number;
}



/**
 * 积分记录列表
 */
export interface IntegralRecordListRes {
    /**
     * @name 剩余
     */
    balance?: number;
    /**
     * @name 途径
     */
    channel?: number;
    /**
     * @name 创建时间
     */
    createTime?: Date;
    /**
     * @name 记录id
     */
    id?: number;
    /**
     * @name 数额
     */
    integral?: number;
    /**
     * @name 手机号
     */
    phone?: string;
    /**
     * @name 备注
     */
    remark?: string;
    /**
     * @name 标题
     */
    title?: string;
    /**
     * @name 类型 1增加  2扣减
     */
    type?: number;
    /**
     * @name 用户id
     */
    uid?: number;
    loading?:boolean;
}
