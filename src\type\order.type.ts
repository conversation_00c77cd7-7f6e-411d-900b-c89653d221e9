import { addPurchaseOrder } from '@/api/order'

/**
 * 订单状态数量
 */
export interface StatusNumParams {
  dateLimit?: string
  type?: number
}

/**
 * 订单状态数量
 */
export interface StatusNumRes {
  /**
   * @name 总数
   */
  all?: number
  /**
   * @name 待评价
   */
  bargain?: number
  /**
   * @name 交易完成
   */
  complete?: number
  /**
   * @name 已删除
   */
  deleted?: number
  /**
   * @name 未发货
   */
  notShipped?: number
  /**
   * @name 已退款
   */
  refunded?: number
  /**
   * @name 退款中
   */
  refunding?: number
  /**
   * @name 0 未退款 1 申请中 2 已退款
   */
  refundStatus?: number
  /**
   * @name 待收货
   */
  spike?: number
  /**
   * @name 待核销
   */
  toBeWrittenOff?: number
  /**
   * @name 未支付
   */
  unPaid?: number
}
/**
 * 订单列表
 */
export interface OrderListParams {
  dateLimit?: string
  limit?: number
  orderNo?: string
  /**
   * @name 订单号
   */
  orderNum?: string
  page?: number
  /**
   * @name 订单状态（0：待发货；1：待收货；2：已收货，待评价；3：已完成；）
   */
  status?: string
  /**
   * @name 订单类型:0-普通订单，1-视频号订单
   */
  type: number
}

/**
 * 订单列表
 */
export interface OrderListRes {
  /**
   * @name 客户名称
   */
  customerName?: string
  /**
   * @name 创建时间
   */
  createTime?: Date
  /**
   * @name 是否改价,0-否，1-是
   */
  isAlterPrice?: boolean
  /**
   * @name 是否删除
   */
  isDel?: boolean
  /**
   * @name 订单id
   */
  orderId?: string
  /**
   * @name 订单号
   */
  orderNum?: string
  /**
   * @name 订单类型
   */
  orderType?: string
  /**
   * @name 支付状态
   */
  paid?: boolean
  /**
   * @name 实际支付金额
   */
  payPrice?: number
  /**
   * @name 支付方式
   */
  payType?: string
  /**
   * @name 支付方式
   */
  payTypeStr?: string
  /**
   * @name 商品信息
   */
  productList?: StoreOrderInfoOldVo[]
  /**
   * @name 用户姓名
   */
  realName?: string
  /**
   * @name 退款金额
   */
  refundPrice?: number
  /**
   * @name 不退款的理由
   */
  refundReason?: string
  /**
   * @name 退款时间
   */
  refundReasonTime?: Date
  /**
   * @name 前台退款原因
   */
  refundReasonWap?: string
  /**
   * @name 退款用户说明
   */
  refundReasonWapExplain?: string
  /**
   * @name 退款图片
   */
  refundReasonWapImg?: string
  /**
   * @name 0 未退款 1 申请中 2 已退款
   */
  refundStatus?: number
  /**
   * @name 订单管理员备注
   */
  remark?: string
  /**
   * @name 订单状态（0：待发货；1：待收货；2：已收货，待评价；3：已完成；）
   */
  status?: number
  /**
   * @name 订单状态
   */
  statusStr?: { [key: string]: string }
  /**
   * @name 订单类型:0-普通订单，1-视频号订单
   */
  type?: number
  /**
   * @name 核销码
   */
  verifyCode?: string
  loading?: boolean
}

/**
 * StoreOrderInfoOldVo
 */
export interface StoreOrderInfoOldVo {
  /**
   * @name id
   */
  id?: number
  info?: OrderInfoDetailVo
  /**
   * @name 订单id
   */
  orderNum?: number
  /**
   * @name 商品ID
   */
  productId?: number
  /**
   * @name 唯一id
   */
  unique?: string
}

/**
 *  订单详情数组
 */
export interface OrderInfoDetailVo {
  attrValueId?: number
  cateId?: number
  giveIntegral?: number
  image?: string
  isExchange?: boolean
  payNum?: number
  price?: number
  productId?: number
  productName?: string
  productType?: number
  sku?: string
  tempId?: number
  vipPrice?: number
  volume?: number
  weight?: number
}

/**
 *  订单详情数组
 */
export interface RefundOrderParams {
  amount?: number
  orderId?: number
  orderNo: string
}

/**
 *  拒绝退款
 */
export interface refuseOrderParams {
  orderNum: string
  reason: string
}

/**
 * 订单详情
 */
export interface OrderInfoRes {
  /**
   * @name 给用户退了多少积分
   */
  backIntegral?: number
  /**
   * @name 优惠券金额
   */
  couponPrice?: number
  /**
   * @name 创建时间
   */
  createTime?: Date
  /**
   * @name 抵扣金额
   */
  deductionPrice?: number
  /**
   * @name 快递单号/手机号
   */
  deliveryId?: string
  /**
   * @name 快递名称/送货人姓名
   */
  deliveryName?: string
  /**
   * @name 发货类型
   */
  deliveryType?: string
  /**
   * @name 订单ID
   */
  id?: number
  /**
   * @name 是否删除
   */
  isDel?: boolean
  /**
   * @name 备注
   */
  mark?: string
  /**
   * @name 用户昵称
   */
  nikeName?: string
  /**
   * @name 订单商品详情
   */
  orderInfo?: StoreOrderInfoOldVo[]
  /**
   * @name 订单号
   */
  orderNum?: string
  /**
   * @name 支付邮费
   */
  payPostage?: number
  /**
   * @name 实际支付金额
   */
  payPrice?: number
  /**
   * @name 支付方式
   */
  payType?: string
  /**
   * @name 支付方式
   */
  payTypeStr?: string
  /**
   * @name 用户电话
   */
  phone?: string
  /**
   * @name 商品总价
   */
  proTotalPrice?: number
  /**
   * @name 用户姓名
   */
  realName?: string
  /**
   * @name 退款金额
   */
  refundPrice?: number
  /**
   * @name 退款时间
   */
  refundReasonTime?: Date
  /**
   * 0 未退款 1 申请中 2 已退款
   */
  refundStatus?: number
  /**
   * @name 管理员备注
   */
  remark?: string
  /**
   * @name 配送方式 1=快递 ，2=门店自提
   */
  shippingType?: number
  /**
   * @name 推广人名称
   */
  spreadName?: string
  /**
   * @name 订单状态（-1 : 申请退款 -2 : 退货成功 0：待发货；1：待收货；2：已收货；3：待评价；-1：已退款）
   */
  status?: number
  /**
   * @name 订单状态
   */
  statusStr?: { [key: string]: string }
  /**
   * @name 订单商品总数
   */
  totalNum?: number
  /**
   * @name 订单总价
   */
  totalPrice?: number
  /**
   * @name 用户id
   */
  uid?: number
  /**
   * @name 使用积分
   */
  useIntegral?: number
  /**
   * @name 详细地址
   */
  userAddress?: string
  /**
   * @name 用户电话
   */
  userPhone?: string
  /**
   * @name 核销码
   */
  verifyCode?: string
}

/**
 * StoreOrderInfoOldVo
 */
export interface StoreOrderInfoOldVo {
  /**
   * @name id
   */
  id?: number
  info?: OrderInfoDetailVo
  /**
   * @name 订单id
   */
  orderNum?: number
  /**
   * @name 商品ID
   */
  productId?: number
  /**
   * @name 唯一id
   */
  unique?: string
}

/**
 * 订单详情数组
 */
export interface OrderInfoDetailVo {
  attrValueId?: number
  cateId?: number
  giveIntegral?: number
  image?: string
  isExchange?: boolean
  payNum?: number
  price?: number
  productId?: number
  productName?: string
  productType?: number
  sku?: string
  tempId?: number
  vipPrice?: number
  volume?: number
  weight?: number
}

/**
 * 采购单请求体
 **/
interface outboundEntityListVo {
  skuId: number
  nums: number[]
  unitPrice?: number // 单价
  price?: number // 金额
}
export interface AddPurchaseOrderVo {
  phone?: string
  mark?: string
  type: number | string
  outboundEntityList: outboundEntityListVo[]
  // 新增的非必填字段
  customerName?: string // 客户名称
  examineApprove?: string // 审批
  examine?: string // 审核
  documentPreparation?: string // 制单
  consignee?: string // 收货人
  driver?: string // 货车司机
}

/**
 * 导出采购订单Excel请求参数
 */
export interface ExcelPurchaseOrderParams {
  /**
   * @name 订单ID数组
   */
  orderIds: number[]
}

/**
 * 智能采购单请求参数
 */
export interface IntelligencePurchaseOrderParams {
  /**
   * @name 提示词
   */
  prompt: string
}

/**
 * 智能采购单商品属性值响应
 */
export interface AttrValueResponse {
  /**
   * @name ID
   */
  id: number
  /**
   * @name 商品ID
   */
  productId: number
  /**
   * @name 商品名称
   */
  productName: string
  /**
   * @name SKU
   */
  suk: string
  /**
   * @name 单位名称数组
   */
  unitNames: string[]
  /**
   * @name 单位数量数组
   */
  unitNums: number[]
  /**
   * @name 单位组合字符串
   */
  unitGroupStr: string
  /**
   * @name 最小单位库存
   */
  minUnitStock: number
  /**
   * @name 商品图片
   */
  image: string
  /**
   * @name 唯一标识
   */
  unique: string
  /**
   * @name 条形码
   */
  barCode: string
  /**
   * @name 重量
   */
  weight: number
  /**
   * @name 体积
   */
  volume: number
  /**
   * @name 属性值
   */
  attrValue: string
  /**
   * @name 是否删除
   */
  isDel: boolean
}

/**
 * 智能采购单响应
 */
export interface IntelligencePurchaseOrderResponse {
  /**
   * @name 提示词
   */
  prompt: string
  /**
   * @name 商品属性值响应列表
   */
  attrValueResponseList: AttrValueResponse[]
  /**
   * @name 失败的提示词列表
   */
  failedPrompts: string[]
}

/**
 * 复制订单响应数据
 */
export interface CopyPurchaseOrderResponse {
  /**
   * @name 电话号码
   */
  phone: string
  /**
   * @name 备注
   */
  mark: string
  /**
   * @name 类型
   */
  type: number
  /**
   * @name SKU列表
   */
  skuList: CopyOrderSkuItem[]
  /**
   * @name 客户名称
   */
  customerName?: string
  /**
   * @name 审批
   */
  examineApprove?: string
  /**
   * @name 审核
   */
  examine?: string
  /**
   * @name 制单
   */
  documentPreparation?: string
  /**
   * @name 收货人
   */
  consignee?: string
  /**
   * @name 司机
   */
  driver?: string
  /**
   * @name 赠品列表
   */
  giftList?: CopyOrderSkuItem[]
}

/**
 * 复制订单SKU项
 */
export interface CopyOrderSkuItem {
  /**
   * @name ID
   */
  id: number
  /**
   * @name 产品编码
   */
  attrValueNo: string
  /**
   * @name 产品ID
   */
  productId: number
  /**
   * @name 产品名称
   */
  productName: string
  /**
   * @name SKU
   */
  suk: string
  /**
   * @name 单位名称列表
   */
  unitNames: string[]
  /**
   * @name 单位数量列表
   */
  unitNums: number[]
  /**
   * @name 变更单位数量列表
   */
  changeUnitNums: number[]
  /**
   * @name 单位组字符串
   */
  unitGroupStr: string
  /**
   * @name 最小单位库存
   */
  minUnitStock: number
  /**
   * @name 图片
   */
  image: string
  /**
   * @name 唯一标识
   */
  unique: string
  /**
   * @name 条形码
   */
  barCode: string
  /**
   * @name 重量
   */
  weight: number
  /**
   * @name 体积
   */
  volume: number
  /**
   * @name 属性值
   */
  attrValue: string
  /**
   * @name 是否删除
   */
  isDel: boolean
}
