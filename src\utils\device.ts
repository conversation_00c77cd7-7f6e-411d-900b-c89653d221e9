/**
 * 设备检测工具函数
 */

/**
 * 检测是否为移动端设备
 * @returns {boolean} 是否为移动端
 */
export function isMobile(): boolean {
  const userAgent = navigator.userAgent.toLowerCase()
  const mobileKeywords = [
    'android',
    'iphone',
    'ipad',
    'ipod',
    'blackberry',
    'windows phone',
    'mobile',
    'webos',
    'opera mini'
  ]
  
  return mobileKeywords.some(keyword => userAgent.includes(keyword))
}

/**
 * 检测是否为平板设备
 * @returns {boolean} 是否为平板
 */
export function isTablet(): boolean {
  const userAgent = navigator.userAgent.toLowerCase()
  return /ipad|android(?!.*mobile)|tablet/i.test(userAgent)
}

/**
 * 检测是否为桌面设备
 * @returns {boolean} 是否为桌面
 */
export function isDesktop(): boolean {
  return !isMobile() && !isTablet()
}

/**
 * 获取设备类型
 * @returns {'mobile' | 'tablet' | 'desktop'} 设备类型
 */
export function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
  if (isMobile()) return 'mobile'
  if (isTablet()) return 'tablet'
  return 'desktop'
}
