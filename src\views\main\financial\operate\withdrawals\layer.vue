<template>
  <Layer :layer="layer" @confirm="submit" ref="layerDom">
    <el-descriptions class="margin-top" :title="form.status == 0 ? '审核中' : form.status == 1 ? '已通过' : '未通过'" :column="3"
      size="default">
      <template #extra>
        <span v-if="form.status == -1"> <el-tag class="ml-2" type="danger">拒绝时间:{{ form.updateTime }}</el-tag> <el-tag
            class="ml-2" type="danger">拒绝理由:{{ form.failMsg }}</el-tag></span>
        <span v-if="form.status == 0">
          <el-button type="success" @click="confirm"> 通过</el-button>
          <el-button type="danger" @click="open">拒绝</el-button>
        </span>
      </template>
      <el-descriptions-item label="id">{{ form.id }}</el-descriptions-item>
      <el-descriptions-item label="uid">{{ form.uid }}</el-descriptions-item>
      <el-descriptions-item label="手机号">{{ form.phone || '无' }}</el-descriptions-item>
      <el-descriptions-item label="提现方式">{{ form.extractType == 'weixin' ? '微信' : '支付宝' }}</el-descriptions-item>
      <el-descriptions-item label="支付宝实名信息">
        账号：{{ userInfo.zhifubaoAccount }},姓名：{{ userInfo.zhifubaoName }}
      </el-descriptions-item>
      <el-descriptions-item label="提现目标">
       {{rechargeTargetList[form.extractTarget]  }}
      </el-descriptions-item>
      <el-descriptions-item label="提现数量">{{ form.extractPrice }}</el-descriptions-item>
      <el-descriptions-item label="备注">{{ form.mark }}</el-descriptions-item>
      <el-descriptions-item label="提现时间">{{ form.createTime }}</el-descriptions-item>

    </el-descriptions>
  </Layer>
</template>

<script lang="ts" setup>
import {rechargeTargetList} from '@/utils/contants'
import type { LayerType } from '@/components/layer/index.vue'
import type { Ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { defineComponent, ref, reactive, defineEmits, defineProps } from 'vue'
import { extractReject, extractConfirm } from '@/api/financial'
import { getUserInfo } from '@/api/userManage'
import type {UserListRes} from '@/type/userManage.type'
import Layer from '@/components/layer/index.vue'
const props = defineProps({
  layer: {
    type: Object,
    default: () => {
      return {
        show: false,
        title: '',
        showButton: true
      }
    }
  }
})
const emit = defineEmits(['getTableData'])
const layerDom: Ref<LayerType | null> = ref(null)
let form = ref({
  id: 0,
  uid: 0,
  realName: '',
  status: null,
  qrcodeUrl: '',
  nickName: '',
  extractType: '',
  extractTarget: '',
  extractPrice: null,
  mark: '',
  detail: '',
  balanceCang: '',
  balanceManageIntegral: '',
  createTime: '',
  updateTime: '',
  failTime: '',
  failMsg: '',
  phone: '',
})
const userInfo = ref<UserListRes>({

});
init()
function init() { // 用于判断新增还是编辑功能
  if (props.layer.row) {
    form.value = JSON.parse(JSON.stringify(props.layer.row)) // 数量量少的直接使用这个转
    getUserInfo({ id: form.value.uid }).then(res => {
      userInfo.value = res
    })
  } else {
  }
}
const open = () => {
  ElMessageBox.prompt('请填写拒绝原因', '', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
  }).then(({ value }) => {
    extractReject({
      id: form.value.id,
      failMsg: value
    }).then(res => {
      ElMessage({
        message: '操作成功',
        type: 'success',
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
  })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: 'Input canceled',
      })
    })
}
const submit = () => {
  layerDom.value && layerDom.value.close()
}
const confirm = () => {
  ElMessageBox.confirm(
    '确定通过该申请提现',
    'success',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'success',
    }
  ).then(() => {
    extractConfirm(form.value.id).then(res => {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      emit('getTableData', false)
      layerDom.value && layerDom.value.close()
    })
  })
    .catch(() => {

    })
}

</script>

<style lang="scss" scoped></style>